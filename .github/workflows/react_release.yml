on:
  release:
    types: [released]

name: React Release

jobs:
  build:
    name: Upload Release Asset
    runs-on: ubuntu-latest
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
          extensions: mysql, mysqlnd, sqlite3, bcmath, gd, curl, zip, openssl, mbstring, xml

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: v5-develop
          fetch-depth: 1

      - name: Install composer dependencies
        run: |
          composer config -g github-oauth.github.com ${{ secrets.GITHUB_TOKEN }}
          composer install --no-dev

      - name: Set current date to variable
        id: set_date
        run: echo "current_date=$(date '+%Y-%m-%d')" >> $GITHUB_ENV

      - name: Prepare React FrontEnd
        run: |
          git clone https://${{secrets.commit_secret}}@github.com/invoiceninja/ui.git
          cd ui
          git checkout develop
          sed -i 's/VITE_IS_TEST=true/VITE_IS_TEST=false/' .env.example
          echo VITE_PUSHER_APP_KEY=f0111a02782708da651f >> .env.example

          cp .env.example .env
          cp ../vite.config.ts.react ./vite.config.js
          sed -i '/"version"/c\  "version": " Latest Build - ${{ env.current_date }}",' package.json
          npm i
          npm run build
          cp -r dist/* ../public/
          mv ../public/index.html ../resources/views/react/index.blade.php

      - name: Prepare JS/CSS assets
        run: |
          npm i
          npm run production

      - name: Cleanup Builds
        run: |
          rm -rf node_modules
          rm -rf .git
          rm .env || true
          rm -rf ui || true
          # Set permissions: directories 755, files 644
          chmod -R a=r,u+w,a+X .

      - name: Build project
        run: |
          shopt -s dotglob
          tar --exclude='public/storage' --exclude='invoiceninja.zip' -zcvf /home/<USER>/work/invoiceninja/invoiceninja.tar *
          cd ../
          tar --exclude='public/storage' --exclude='invoiceninja.zip' -zcvf invoiceninja.tar.gz invoiceninja/
      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: |
            /home/<USER>/work/invoiceninja/invoiceninja.tar
            /home/<USER>/work/invoiceninja/invoiceninja.tar.gz
