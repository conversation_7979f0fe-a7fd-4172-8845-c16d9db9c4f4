<?php

/**
 * Ozoo ERP Setup Script
 * This script bypasses the web setup and configures the system directly
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;
use App\Models\Account;
use App\Models\Company;
use App\Models\User;

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚀 Starting Ozoo ERP Setup...\n";

try {
    // Test database connection
    echo "📡 Testing database connection...\n";
    DB::connection()->getPdo();
    echo "✅ Database connection successful!\n";

    // Check if setup is already done
    $existingAccount = DB::table('accounts')->first();
    if ($existingAccount) {
        echo "⚠️  Setup already completed. Account exists.\n";
        echo "🌐 You can access Ozoo ERP at: http://127.0.0.1:8000\n";
        exit(0);
    }

    // Create account
    echo "🏢 Creating account...\n";
    $accountId = DB::table('accounts')->insertGetId([
        'account_key' => 'ozoo-erp-' . uniqid(),
        'trial_started' => now(),
        'trial_plan' => 'pro',
        'plan_term' => 'yearly',
        'plan_started' => now(),
        'plan_paid' => now(),
        'plan_expires' => now()->addYear(),
        'trial_duration' => 365,
        'hosted_client_count' => 0,
        'hosted_company_count' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    echo "✅ Account created with ID: $accountId\n";

    // Create company
    echo "🏭 Creating company...\n";
    $companyId = DB::table('companies')->insertGetId([
        'name' => 'Ozoo ERP Company',
        'email' => '<EMAIL>',
        'website' => 'http://127.0.0.1:8000',
        'phone' => '+**********',
        'address1' => '123 Business Street',
        'city' => 'Business City',
        'state' => 'Business State',
        'postal_code' => '12345',
        'country_id' => 1,
        'currency_id' => 1,
        'language_id' => 1,
        'timezone_id' => 1,
        'date_format_id' => 1,
        'datetime_format_id' => 1,
        'industry_id' => 1,
        'size_id' => 1,
        'custom_fields' => json_encode([]),
        'settings' => json_encode([
            'name' => 'Ozoo ERP Company',
            'website' => 'http://127.0.0.1:8000',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'address1' => '123 Business Street',
            'city' => 'Business City',
            'state' => 'Business State',
            'postal_code' => '12345',
            'country_id' => 1,
            'currency_id' => 1,
            'language_id' => 1,
            'timezone_id' => 1,
            'date_format_id' => 1,
            'datetime_format_id' => 1,
        ]),
        'is_disabled' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    echo "✅ Company created with ID: $companyId\n";

    // Create admin user
    echo "👤 Creating admin user...\n";
    $userId = DB::table('users')->insertGetId([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'email_verified_at' => now(),
        'password' => Hash::make('admin123'),
        'confirmed' => true,
        'has_password' => true,
        'language_id' => 1,
        'user_logged_in_notification' => true,
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    echo "✅ Admin user created with ID: $userId\n";

    echo "\n🎉 Ozoo ERP Setup Complete!\n";
    echo "📧 Email: <EMAIL>\n";
    echo "🔑 Password: admin123\n";
    echo "🌐 URL: http://127.0.0.1:8000\n";
    echo "\n⚠️  Please change the default password after first login!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📝 Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}