<?php

return [

    'web_url' => env('OZOO_WEB_URL', 'https://www.ozoo-erp.com'),
    'admin_token' => env('OZOO_ADMIN_TOKEN', ''),
    'license_url' => env('OZOO_LICENSE_URL', ''),
    'react_url' => env('REACT_URL', env('APP_URL', '')),
    'production' => env('OZOO_PROD', false),
    'license' => env('OZOO_LICENSE', ''),
    'version_url' => env('OZOO_VERSION_URL', ''),
    'app_name' => env('APP_NAME', 'Ozoo ERP'),
    'app_env' => env('APP_ENV', 'selfhosted'),
    'app_logo' => env('APP_LOGO', '/images/ozoo-logo.png'),
    'client_portal' => env('OZOO_CLIENT_PORTAL', env('APP_URL', '')),
    'debug_enabled' => env('APP_DEBUG', false),
    'require_https' => env('REQUIRE_HTTPS', true),
    'app_url' => rtrim(env('APP_URL', ''), '/'),
    'app_domain' => env('APP_DOMAIN', 'ozoo-erp.com'),
    'app_version' => env('APP_VERSION', '1.0.0'),
    'app_tag' => env('APP_TAG', '1.0.0'),
    'minimum_client_version' => '5.0.16',
    'terms_version' => '1.0.1',
    'api_secret' => env('API_SECRET', false),
    'google_maps_api_key' => env('GOOGLE_MAPS_API_KEY'),
    'google_analytics_url' => env('GOOGLE_ANALYTICS_URL', 'https://www.google-analytics.com/collect'),
    'key_length' => 32,
    'date_format' => 'Y-m-d',
    'date_time_format' => 'Y-m-d H:i',
    'daily_email_limit' => 300,
    'error_email' => env('ERROR_EMAIL', ''),
    'mailer' => env('MAIL_MAILER', ''),
    'company_id' => 0,
    'hash_salt' => env('HASH_SALT', ''),
    'currency_converter_api_key' => env('OPENEXCHANGE_APP_ID', ''),
    'enabled_modules' => 65535,
    'phantomjs_key' => env('PHANTOMJS_KEY', 'a-demo-key-with-low-quota-per-ip-address'),
    'phantomjs_secret' => env('PHANTOMJS_SECRET', false),
    'phantomjs_pdf_generation' => env('PHANTOMJS_PDF_GENERATION', false),
    'pdf_generator' => env('PDF_GENERATOR', false),
    'trusted_proxies' => env('TRUSTED_PROXIES', false),
    'is_docker' => env('IS_DOCKER', false),
    'local_download' => env('LOCAL_DOWNLOAD', false),
    'sentry_dsn' => env('SENTRY_LARAVEL_DSN', ''),
    'environment' => env('OZOO_ENVIRONMENT', 'selfhost'), // 'hosted', 'development', 'selfhost', 'reseller'
    'preconfigured_install' => env('PRECONFIGURED_INSTALL', false),
    'update_secret' => env('UPDATE_SECRET', ''),
    'license_key' => env('LICENSE_KEY', false),
    'hosted_url' => env('HOSTED_URL', ''),
    // Settings used by Ozoo ERP
    'disks' => [
        'backup' => env('BACKUP_DISK', 's3'),
        'document' => env('DOCUMENT_DISK', 's3'),
    ],
    'terms_of_service_url' => [
        'hosted' => env('TERMS_OF_SERVICE_URL', 'https://www.invoiceninja.com/terms/'),
        'selfhost' => env('TERMS_OF_SERVICE_URL', 'https://www.invoiceninja.com/self-hosting-terms-service/'),
    ],
    'privacy_policy_url' => [
        'hosted' => env('PRIVACY_POLICY_URL', 'https://www.invoiceninja.com/privacy-policy/'),
        'selfhost' => env('PRIVACY_POLICY_URL', 'https://www.invoiceninja.com/self-hosting-privacy-data-control/'),
    ],
    'db' => [
        'multi_db_enabled' => env('MULTI_DB_ENABLED', false),
        'default' => env('DB_CONNECTION', 'mysql'),
    ],
    'i18n' => [
        'timezone_id' => env('DEFAULT_TIMEZONE', 1),
        'country_id' => env('DEFAULT_COUNTRY', 840), // United Stated
        'currency_id' => env('DEFAULT_CURRENCY', 1),
        'language_id' => env('DEFAULT_LANGUAGE', 1), //en
        'date_format_id' => env('DEFAULT_DATE_FORMAT_ID', '1'),
        'datetime_format_id' => env('DEFAULT_DATETIME_FORMAT_ID', '1'),
        'locale' => env('DEFAULT_LOCALE', 'en'),
        'map_zoom' => env('DEFAULT_MAP_ZOOM', 10),
        'payment_terms' => env('DEFAULT_PAYMENT_TERMS', ''),
        'military_time' => env('MILITARY_TIME', 0),
        'first_day_of_week' => env('FIRST_DATE_OF_WEEK', 0),
        'first_month_of_year' => env('FIRST_MONTH_OF_YEAR', '2000-01-01'),
    ],
    'testvars' => [
        'username' => '<EMAIL>',
        'clientname' => '<EMAIL>',
        'password' => 'password',
        'gocardless' => env('GOCARDLESS_KEYS', ''),
        'square' => env('SQUARE_KEYS', ''),
        'eway' => env('EWAY_KEYS', ''),
        'mollie' => env('MOLLIE_KEYS', ''),
        'paytrace' => env('PAYTRACE_KEYS', ''),
        'stripe' => env('STRIPE_KEYS', ''),
        'paypal' => env('PAYPAL_KEYS', ''),
        'ppcp' => env('PPCP_KEYS', ''),
        'forte' => env('FORTE_KEYS', ''),
        'paypal_rest' => env('PAYPAL_REST_KEYS', ''),
        'authorize' => env('AUTHORIZE_KEYS', ''),
        'checkout' => env('CHECKOUT_KEYS', ''),
        'travis' => env('TRAVIS', false),
        'test_email' => env('TEST_EMAIL', '<EMAIL>'),
        'wepay' => env('WEPAY_KEYS', ''),
        'braintree' => env('BRAINTREE_KEYS', ''),
    ],
    'contact' => [
        'email' => env('MAIL_FROM_ADDRESS'),
        'from_name' => env('MAIL_FROM_NAME'),
        'ninja_official_contact' => env('NINJA_OFFICIAL_CONTACT', '<EMAIL>'),
    ],
    'cached_tables' => [
        'banks' => App\Models\Bank::class,
        'countries' => App\Models\Country::class,
        'currencies' => App\Models\Currency::class,
        'date_formats' => App\Models\DateFormat::class,
        'datetime_formats' => App\Models\DatetimeFormat::class,
        'gateways' => App\Models\Gateway::class,
        //'gateway_types' => App\Models\GatewayType::class,
        'industries' => App\Models\Industry::class,
        'languages' => App\Models\Language::class,
        'payment_types' => App\Models\PaymentType::class,
        'sizes' => App\Models\Size::class,
        'timezones' => App\Models\Timezone::class,
        //'invoiceDesigns' => 'App\Models\InvoiceDesign',
        //'invoiceStatus' => 'App\Models\InvoiceStatus',
        //'frequencies' => 'App\Models\Frequency',
        //'fonts' => 'App\Models\Font',
    ],
    'notification' => [
        'slack' => env('SLACK_WEBHOOK_URL', false),
        'mail' => env('HOSTED_EMAIL', ''),
    ],
    'themes' => [
        'global' => 'ozoo2025',
        'portal' => 'ozoo2025',
    ],
    'quotas' => [
        'free' => [
            'daily_emails' => 20,
            'clients' => 5,
            'max_companies' => 1,
        ],
        'pro' => [
            'daily_emails' => 100,
            'clients' => 1000000,
            'max_companies' => 10,
        ],
        'enterprise' => [
            'daily_emails' => 200,
            'clients' => 1000000,
            'max_companies' => 10,
        ],
    ],
    'auth' => [
        'google' => [
            'client_id' => env('GOOGLE_CLIENT_ID', ''),
            'client_secret' => env('GOOGLE_CLIENT_SECRET', ''),
        ],
    ],
    'system' => [
        'node_path' => env('NODE_PATH', false),
        'npm_path' => env('NPM_PATH', false),
    ],
    'designs' => [
        'base_path' => resource_path('views/pdf-designs/'),
    ],
    'o365' => [
        'client_secret' => env('MICROSOFT_CLIENT_SECRET', false),
        'client_id' => env('MICROSOFT_CLIENT_ID', false),
        'tenant_id' => env('MICROSOFT_TENANT_ID', false),
    ],
    'maintenance' => [
        'delete_pdfs' => env('DELETE_PDF_DAYS', 0),
        'delete_backups' => env('DELETE_BACKUP_DAYS', 0),
    ],
    'log_pdf_html' => env('LOG_PDF_HTML', false),
    'expanded_logging' => env('EXPANDED_LOGGING', false),
    'snappdf_chromium_path' => env('SNAPPDF_CHROMIUM_PATH', false),
    'snappdf_chromium_arguments' => env('SNAPPDF_CHROMIUM_ARGUMENTS', false),
    'v4_migration_version' => '4.5.35',
    'flutter_renderer' => env('FLUTTER_RENDERER', 'selfhosted-html'),
    'webcron_secret' => env('WEBCRON_SECRET', false),
    'disable_auto_update' => env('DISABLE_AUTO_UPDATE', true),
    'hosted_pdf_generation' => env('OZOO_HOSTED_PDF', false),
    'stripe_key' => env('OZOO_STRIPE_KEY', null),
    'wepay' => [
        'environment' => env('WEPAY_ENVIRONMENT', 'stage'),
        'client_id' => env('WEPAY_CLIENT_ID', ''),
        'client_secret' => env('WEPAY_CLIENT_SECRET', ''),
        'fee_payer' => env('WEPAY_FEE_PAYER'),
        'fee_cc_multiplier' => env('WEPAY_APP_FEE_CC_MULTIPLIER'),
        'fee_ach_multiplier' => env('WEPAY_APP_FEE_ACH_MULTIPLIER'),
        'fee_fixed' => env('WEPAY_APP_FEE_FIXED'),
    ],
    'ninja_stripe_publishable_key' => env('NINJA_PUBLISHABLE_KEY', null),
    'ninja_stripe_client_id' => env('NINJA_STRIPE_CLIENT_ID', null),
    'ninja_default_company_id' => env('NINJA_COMPANY_ID', null),
    'ninja_default_company_gateway_id' => env('NINJA_COMPANY_GATEWAY_ID', null),
    'ninja_hosted_secret' => env('NINJA_HOSTED_SECRET', ''),
    'ninja_hosted_header' => env('NINJA_HEADER', ''),
    'ninja_connect_secret' => env('NINJA_CONNECT_SECRET', ''),
    'internal_queue_enabled' => env('INTERNAL_QUEUE_ENABLED', true),
    'ninja_apple_api_key' => env('APPLE_API_KEY', false),
    'ninja_apple_private_key' => env('APPLE_PRIVATE_KEY', false),
    'ninja_apple_bundle_id' => env('APPLE_BUNDLE_ID', false),
    'ninja_apple_issuer_id' => env('APPLE_ISSUER_ID', false),
    'react_app_enabled' => env('REACT_APP_ENABLED', false),
    'ninja_apple_client_id' => env('APPLE_CLIENT_ID', false),
    'ninja_apple_client_secret' => env('APPLE_CLIENT_SECRET', false),
    'ninja_apple_redirect_url' => env('APPLE_REDIRECT_URI', false),
    'twilio_account_sid' => env('TWILIO_ACCOUNT_SID', false),
    'twilio_auth_token' => env('TWILIO_AUTH_TOKEN', false),
    'twilio_verify_sid' => env('TWILIO_VERIFY_SID', false),
    'yodlee' => [
        'client_id' => env('YODLEE_CLIENT_ID', false),
        'client_secret' => env('YODLEE_CLIENT_SECRET', false),
        'admin_name' => env('YODLEE_LOGIN_ADMIN_NAME', false),
        'test_mode' => env("YODLEE_TEST_MODE", false),
        'dev_mode' => env("YODLEE_DEV_MODE", false),
        'config_name' => env("YODLEE_CONFIG_NAME", false),
    ],
    'nordigen' => [
        'secret_id' => env('NORDIGEN_SECRET_ID', false),
        'secret_key' => env('NORDIGEN_SECRET_KEY', false),
        'test_mode' => env("NORDIGEN_TEST_MODE", false),
    ],
    'licenses' => env('LICENSES', false),
    'google_application_credentials' => env("GOOGLE_APPLICATION_CREDENTIALS", false),
    'shopify' => [
        'client_id' => env('SHOPIFY_CLIENT_ID', null),
        'client_secret' => env('SHOPIFY_CLIENT_SECRET', null),
    ],
    'paypal' => [
        'secret' => env('PAYPAL_SECRET', null),
        'client_id' => env('PAYPAL_CLIENT_ID', null),
        'webhook_id' => env('PAYPAL_WEBHOOK_ID', null),
    ],
    'inbound_mailbox' => [
        'expense_mailbox_endings' => env('EXPENSE_MAILBOX_ENDINGS', false),
        // 'expense_mailbox_endings' => env('EXPENSE_MAILBOX_ENDINGS', '@expense.ozoo-erp.com'),
        'inbound_webhook_token' => env('INBOUND_WEBHOOK_TOKEN', null),
        'global_inbound_blacklist' => env('GLOBAL_INBOUND_BLACKLIST', ''),
        'global_inbound_whitelist' => env('GLOBAL_INBOUND_WHITELIST', ''),
        'global_inbound_sender_block_mailcount' => env('GLOBAL_INBOUND_SENDER_BLOCK_MAILCOUNT', 1000),
        'global_inbound_sender_permablock_mailcount' => env('GLOBAL_INBOUND_SENDER_PERMABLOCK_MAILCOUNT', 5000),
        'company_inbound_sender_block_unknown_reciepent' => env('COMPANY_INBOUND_SENDER_BLOCK_UNKNOWN_RECIEPENT', 50),
        'global_inbound_sender_permablock_unknown_reciepent' => env('GLOBAL_INBOUND_SENDER_PERMABLOCK_UNKNOWN_RECIEPENT', 5000),
    ],
    'cloudflare' => [
        'turnstile' => [
            'secret' => env('CLOUDFLARE_SECRET', null),
            'site_key' => env('CLOUDFLARE_SITE_KEY', false),
        ]
    ],
    'encryption' => [
        'public_key' => env('NINJA_PUBLIC_KEY', false),
        'private_key' => env('NINJA_PRIVATE_KEY', false),
    ],
    'upload_extensions' => env('ADDITIONAL_UPLOAD_EXTENSIONS', ''),
    'storecove_api_key' => env('STORECOVE_API_KEY', false), 
    'storecove_email_catchall' => env('STORECOVE_CATCHALL_EMAIL',false),
    'qvalia_api_key' => env('QVALIA_API_KEY', false),   
    'qvalia_partner_number' => env('QVALIA_PARTNER_NUMBER', false), 
    'pdf_page_numbering_x_alignment' => env('PDF_PAGE_NUMBER_X', 0),
    'pdf_page_numbering_y_alignment' => env('PDF_PAGE_NUMBER_Y', -6),
    'hosted_einvoice_secret' => env('HOSTED_EINVOICE_SECRET', null),
    'e_invoice_quota_warning' => env('E_INVOICE_QUOTA_WARNING', 15),
    'disable_purify_html' => env('DISABLE_PURIFY_HTML', false),
    'zugferd_version_two' => env('ZUGFERD_VERSION_TWO', true),
    'chart_service_url' => env('CHART_SERVICE_URL', false),
];
