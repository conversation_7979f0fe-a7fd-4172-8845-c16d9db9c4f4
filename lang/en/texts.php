<?php

$lang = array(
    'organization' => 'Organization',
    'name' => 'Name',
    'website' => 'Website',
    'work_phone' => 'Phone',
    'address' => 'Address',
    'address1' => 'Street',
    'address2' => 'Apt/Suite',
    'city' => 'City',
    'state' => 'State/Province',
    'postal_code' => 'Postal Code',
    'country_id' => 'Country',
    'contacts' => 'Contacts',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'phone' => 'Phone',
    'email' => 'Email',
    'additional_info' => 'Additional Info',
    'payment_terms' => 'Payment Terms',
    'currency_id' => 'Currency',
    'size_id' => 'Company Size',
    'industry_id' => 'Industry',
    'private_notes' => 'Private Notes',
    'invoice_date' => 'Invoice Date',
    'due_date' => 'Due Date',
    'invoice' => 'Invoice',
    'client' => 'Client',
    'invoice_number' => 'Invoice Number',
    'invoice_number_short' => 'Invoice #',
    'po_number' => 'PO Number',
    'po_number_short' => 'PO #',
    'frequency_id' => 'How Often',
    'discount' => 'Discount',
    'taxes' => 'Taxes',
    'tax' => 'Tax',
    'item' => 'Item',
    'description' => 'Description',
    'unit_cost' => 'Unit Cost',
    'quantity' => 'Quantity',
    'line_total' => 'Line Total',
    'subtotal' => 'Subtotal',
    'net_subtotal' => 'Net',
    'paid_to_date' => 'Paid to Date',
    'balance_due' => 'Balance Due',
    'invoice_design_id' => 'Design',
    'terms' => 'Terms',
    'your_invoice' => 'Your Invoice',
    'remove_contact' => 'Remove contact',
    'add_contact' => 'Add contact',
    'create_new_client' => 'Create new client',
    'edit_client_details' => 'Edit client details',
    'enable' => 'Enable',
    'learn_more' => 'Learn more',
    'manage_rates' => 'Manage rates',
    'note_to_client' => 'Note to Client',
    'invoice_terms' => 'Invoice Terms',
    'save_as_default_terms' => 'Save as default terms',
    'download_pdf' => 'Download PDF',
    'pay_now' => 'Pay Now',
    'save_invoice' => 'Save Invoice',
    'clone_invoice' => 'Clone To Invoice',
    'archive_invoice' => 'Archive Invoice',
    'delete_invoice' => 'Delete Invoice',
    'email_invoice' => 'Email Invoice',
    'enter_payment' => 'Enter Payment',
    'tax_rates' => 'Tax Rates',
    'rate' => 'Rate',
    'settings' => 'Settings',
    'enable_invoice_tax' => 'Enable specifying an <b>invoice tax</b>',
    'enable_line_item_tax' => 'Enable specifying <b>line item taxes</b>',
    'dashboard' => 'Dashboard',
    'dashboard_totals_in_all_currencies_help' => 'Note: add a :link named ":name" to show the totals using a single base currency.',
    'clients' => 'Clients',
    'invoices' => 'Invoices',
    'payments' => 'Payments',
    'credits' => 'Credits',
    'history' => 'History',
    'search' => 'Search',
    'sign_up' => 'Sign Up',
    'guest' => 'Guest',
    'company_details' => 'Company Details',
    'online_payments' => 'Online Payments',
    'notifications' => 'Notifications',
    'import_export' => 'Import | Export',
    'done' => 'Done',
    'save' => 'Save',
    'create' => 'Create',
    'upload' => 'Upload',
    'import' => 'Import',
    'download' => 'Download',
    'cancel' => 'Cancel',
    'close' => 'Close',
    'provide_email' => 'Please provide a valid email address',
    'powered_by' => 'Powered by',
    'no_items' => 'No items',
    'recurring_invoices' => 'Recurring Invoices',
    'recurring_help' => '<p>Automatically send clients the same invoices weekly, bi-monthly, monthly, quarterly or annually. </p>
        <p>Use :MONTH, :QUARTER or :YEAR for dynamic dates. Basic math works as well, for example :MONTH-1.</p>
        <p>Examples of dynamic invoice variables:</p>
        <ul>
          <li>"Gym membership for the month of :MONTH" >> "Gym membership for the month of July"</li>
          <li>":YEAR+1 yearly subscription" >> "2015 Yearly Subscription"</li>
          <li>"Retainer payment for :QUARTER+1" >> "Retainer payment for Q2"</li>
        </ul>',
    'recurring_quotes' => 'Recurring Quotes',
    'in_total_revenue' => 'in total revenue',
    'billed_client' => 'billed client',
    'billed_clients' => 'billed clients',
    'active_client' => 'active client',
    'active_clients' => 'active clients',
    'invoices_past_due' => 'Invoices Past Due',
    'upcoming_invoices' => 'Upcoming Invoices',
    'average_invoice' => 'Average Invoice',
    'archive' => 'Archive',
    'delete' => 'Delete',
    'archive_client' => 'Archive Client',
    'delete_client' => 'Delete Client',
    'archive_payment' => 'Archive Payment',
    'delete_payment' => 'Delete Payment',
    'archive_credit' => 'Archive Credit',
    'delete_credit' => 'Delete Credit',
    'show_archived_deleted' => 'Show archived/deleted',
    'filter' => 'Filter',
    'new_client' => 'New Client',
    'new_invoice' => 'New Invoice',
    'new_payment' => 'Enter Payment',
    'new_credit' => 'Enter Credit',
    'contact' => 'Contact',
    'date_created' => 'Date Created',
    'last_login' => 'Last Login',
    'balance' => 'Balance',
    'action' => 'Action',
    'status' => 'Status',
    'invoice_total' => 'Invoice Total',
    'frequency' => 'Frequency',
    'range' => 'Range',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'transaction_reference' => 'Transaction Reference',
    'method' => 'Method',
    'payment_amount' => 'Payment Amount',
    'payment_date' => 'Payment Date',
    'credit_amount' => 'Credit Amount',
    'credit_balance' => 'Credit Balance',
    'credit_date' => 'Credit Date',
    'empty_table' => 'No data available in table',
    'select' => 'Select',
    'edit_client' => 'Edit Client',
    'edit_invoice' => 'Edit Invoice',
    'create_invoice' => 'Create Invoice',
    'enter_credit' => 'Enter Credit',
    'last_logged_in' => 'Last logged in',
    'details' => 'Details',
    'standing' => 'Standing',
    'credit' => 'Credit',
    'activity' => 'Activity',
    'date' => 'Date',
    'message' => 'Message',
    'adjustment' => 'Adjustment',
    'are_you_sure' => 'Are you sure?',
    'payment_type_id' => 'Payment Type',
    'amount' => 'Amount',
    'work_email' => 'Email',
    'language_id' => 'Language',
    'timezone_id' => 'Timezone',
    'date_format_id' => 'Date Format',
    'datetime_format_id' => 'Date/Time Format',
    'users' => 'Users',
    'localization' => 'Localization',
    'remove_logo' => 'Remove logo',
    'logo_help' => 'Supported: JPEG, GIF and PNG',
    'payment_gateway' => 'Payment Gateway',
    'gateway_id' => 'Gateway',
    'email_notifications' => 'Email Notifications',
    'email_viewed' => 'Email me when an invoice is <b>viewed</b>',
    'email_paid' => 'Email me when an invoice is <b>paid</b>',
    'site_updates' => 'Site Updates',
    'custom_messages' => 'Custom Messages',
    'default_email_footer' => 'Set default <b>email signature</b>',
    'select_file' => 'Please select a file',
    'first_row_headers' => 'Use first row as headers',
    'column' => 'Column',
    'sample' => 'Sample',
    'import_to' => 'Import to',
    'client_will_create' => 'client will be created',
    'clients_will_create' => 'clients will be created',
    'email_settings' => 'Email Settings',
    'client_view_styling' => 'Client View Styling',
    'pdf_email_attachment' => 'Attach PDF',
    'custom_css' => 'Custom CSS',
    'import_clients' => 'Import Client Data',
    'csv_file' => 'CSV file',
    'export_clients' => 'Export Client Data',
    'created_client' => 'Successfully created client',
    'created_clients' => 'Successfully created :count client(s)',
    'updated_settings' => 'Successfully updated settings',
    'removed_logo' => 'Successfully removed logo',
    'sent_message' => 'Successfully sent message',
    'invoice_error' => 'Please make sure to select a client and correct any errors',
    'limit_clients' => 'You\'ve hit the :count client limit on Free accounts. Congrats on your success!',
    'payment_error' => 'There was an error processing your payment. Please try again later.',
    'registration_required' => 'Registration Required',
    'confirmation_required' => 'Please confirm your email address, :link to resend the confirmation email.',
    'updated_client' => 'Successfully updated client',
    'archived_client' => 'Successfully archived client',
    'archived_clients' => 'Successfully archived :count clients',
    'deleted_client' => 'Successfully deleted client',
    'deleted_clients' => 'Successfully deleted :count clients',
    'updated_invoice' => 'Successfully updated invoice',
    'created_invoice' => 'Successfully created invoice',
    'cloned_invoice' => 'Successfully cloned invoice',
    'emailed_invoice' => 'Successfully emailed invoice',
    'and_created_client' => 'and created client',
    'archived_invoice' => 'Successfully archived invoice',
    'archived_invoices' => 'Successfully archived :count invoices',
    'deleted_invoice' => 'Successfully deleted invoice',
    'deleted_invoices' => 'Successfully deleted :count invoices',
    'created_payment' => 'Successfully created payment',
    'created_payments' => 'Successfully created :count payment(s)',
    'archived_payment' => 'Successfully archived payment',
    'archived_payments' => 'Successfully archived :count payments',
    'deleted_payment' => 'Successfully deleted payment',
    'deleted_payments' => 'Successfully deleted :count payments',
    'applied_payment' => 'Successfully applied payment',
    'created_credit' => 'Successfully created credit',
    'archived_credit' => 'Successfully archived credit',
    'archived_credits' => 'Successfully archived :count credits',
    'deleted_credit' => 'Successfully deleted credit',
    'deleted_credits' => 'Successfully deleted :count credits',
    'imported_file' => 'Successfully imported file',
    'updated_vendor' => 'Successfully updated vendor',
    'created_vendor' => 'Successfully created vendor',
    'archived_vendor' => 'Successfully archived vendor',
    'archived_vendors' => 'Successfully archived :count vendors',
    'deleted_vendor' => 'Successfully deleted vendor',
    'deleted_vendors' => 'Successfully deleted :count vendors',
    'confirmation_subject' => 'Account Confirmation',
    'confirmation_header' => 'Account Confirmation',
    'confirmation_message' => 'Please access the link below to confirm your account.',
    'invoice_subject' => 'New invoice :number from :account',
    'invoice_message' => 'To view your invoice for :amount, click the link below.',
    'payment_subject' => 'Payment Received',
    'payment_message' => 'Thank you for your payment of :amount.',
    'email_salutation' => 'Dear :name,',
    'email_signature' => 'Regards,',
    'email_from' => 'The Invoice Ninja Team',
    'invoice_link_message' => 'To view the invoice click the link below:',
    'notification_invoice_paid_subject' => 'Invoice :invoice was paid by :client',
    'notification_invoice_sent_subject' => 'Invoice :invoice was sent to :client',
    'notification_invoice_viewed_subject' => 'Invoice :invoice was viewed by :client',
    'notification_invoice_paid' => 'A payment of :amount was made by client :client towards Invoice :invoice.',
    'notification_invoice_sent' => 'The following client :client was emailed Invoice :invoice for :amount.',
    'notification_invoice_viewed' => 'The following client :client viewed Invoice :invoice for :amount.',
    'stripe_payment_text' => 'Invoice :invoicenumber for :amount for client :client',
    'stripe_payment_text_without_invoice' => 'Payment with no invoice for amount :amount for client :client',
    'reset_password' => 'You can reset your account password by clicking the following button:',
    'secure_payment' => 'Secure Payment',
    'card_number' => 'Card Number',
    'expiration_month' => 'Expiration Month',
    'expiration_year' => 'Expiration Year',
    'cvv' => 'CVV',
    'logout' => 'Log Out',
    'sign_up_to_save' => 'Sign up to save your work',
    'agree_to_terms' => 'I agree to the :terms',
    'terms_of_service' => 'Terms of Service',
    'email_taken' => 'The email address is already registered',
    'working' => 'Working',
    'success' => 'Success',
    'success_message' => 'You have successfully registered! Please visit the link in the account confirmation email to verify your email address.',
    'erase_data' => 'Your account is not registered, this will permanently erase your data.',
    'password' => 'Password',
    'pro_plan_product' => 'Pro Plan',
    'unsaved_changes' => 'You have unsaved changes',
    'custom_fields' => 'Custom Fields',
    'company_fields' => 'Company Fields',
    'client_fields' => 'Client Fields',
    'field_label' => 'Field Label',
    'field_value' => 'Field Value',
    'edit' => 'Edit',
    'set_name' => 'Set your company name',
    'view_as_recipient' => 'View as recipient',
    'product_library' => 'Product Library',
    'product' => 'Product',
    'products' => 'Products',
    'fill_products' => 'Auto-fill products',
    'fill_products_help' => 'Selecting a product will automatically <b>fill in the description and cost</b>',
    'update_products' => 'Auto-update products',
    'update_products_help' => 'Updating an invoice will automatically <b>update the product library</b>',
    'create_product' => 'Add Product',
    'edit_product' => 'Edit Product',
    'archive_product' => 'Archive Product',
    'updated_product' => 'Successfully updated product',
    'created_product' => 'Successfully created product',
    'archived_product' => 'Successfully archived product',
    'pro_plan_custom_fields' => ':link to enable custom fields by joining the Pro Plan',
    'advanced_settings' => 'Advanced Settings',
    'pro_plan_advanced_settings' => ':link to enable the advanced settings by joining the Pro Plan',
    'invoice_design' => 'Invoice Design',
    'specify_colors' => 'Specify colors',
    'specify_colors_label' => 'Select the colors used in the invoice',
    'chart_builder' => 'Chart Builder',
    'ninja_email_footer' => 'Created by :site | Create. Send. Get Paid.',
    'go_pro' => 'Go Pro',
    'quote' => 'Quote',
    'quotes' => 'Quotes',
    'quote_number' => 'Quote Number',
    'quote_number_short' => 'Quote #',
    'quote_date' => 'Quote Date',
    'quote_total' => 'Quote Total',
    'your_quote' => 'Your Quote',
    'total' => 'Total',
    'clone' => 'Clone',
    'new_quote' => 'New Quote',
    'create_quote' => 'Create Quote',
    'edit_quote' => 'Edit Quote',
    'archive_quote' => 'Archive Quote',
    'delete_quote' => 'Delete Quote',
    'save_quote' => 'Save Quote',
    'email_quote' => 'Email Quote',
    'clone_quote' => 'Clone To Quote',
    'convert_to_invoice' => 'Convert to Invoice',
    'view_invoice' => 'View Invoice',
    'view_client' => 'View Client',
    'view_quote' => 'View Quote',
    'updated_quote' => 'Successfully updated quote',
    'created_quote' => 'Successfully created quote',
    'cloned_quote' => 'Successfully cloned quote',
    'emailed_quote' => 'Successfully emailed quote',
    'archived_quote' => 'Successfully archived quote',
    'archived_quotes' => 'Successfully archived :count quotes',
    'deleted_quote' => 'Successfully deleted quote',
    'deleted_quotes' => 'Successfully deleted :count quotes',
    'converted_to_invoice' => 'Successfully converted quote to invoice',
    'quote_subject' => 'New quote :number from :account',
    'quote_message' => 'To view your quote for :amount, click the link below.',
    'quote_link_message' => 'To view your client quote click the link below:',
    'notification_quote_sent_subject' => 'Quote :invoice was sent to :client',
    'notification_quote_viewed_subject' => 'Quote :invoice was viewed by :client',
    'notification_quote_sent' => 'The following client :client was emailed Quote :invoice for :amount.',
    'notification_quote_viewed' => 'The following client :client viewed Quote :invoice for :amount.',
    'session_expired' => 'Your session has expired.',
    'invoice_fields' => 'Invoice Fields',
    'invoice_options' => 'Invoice Options',
    'hide_paid_to_date' => 'Hide Paid to Date',
    'hide_paid_to_date_help' => 'Only display the "Paid to Date" area on your invoices once a payment has been received.',
    'charge_taxes' => 'Charge taxes',
    'user_management' => 'User Management',
    'add_user' => 'Add User',
    'send_invite' => 'Send Invitation',
    'sent_invite' => 'Successfully sent invitation',
    'updated_user' => 'Successfully updated user',
    'invitation_message' => 'You\'ve been invited by :invitor. ',
    'register_to_add_user' => 'Please sign up to add a user',
    'user_state' => 'State',
    'edit_user' => 'Edit User',
    'delete_user' => 'Delete User',
    'active' => 'Active',
    'pending' => 'Pending',
    'deleted_user' => 'Successfully deleted user',
    'confirm_email_invoice' => 'Are you sure you want to email this invoice?',
    'confirm_email_quote' => 'Are you sure you want to email this quote?',
    'confirm_recurring_email_invoice' => 'Are you sure you want this invoice emailed?',
    'confirm_recurring_email_invoice_not_sent' => 'Are you sure you want to start the recurrence?',
    'cancel_account' => 'Delete Account',
    'cancel_account_message' => 'Warning: This will permanently delete your account, there is no undo.',
    'go_back' => 'Go Back',
    'data_visualizations' => 'Data Visualizations',
    'sample_data' => 'Sample data shown',
    'hide' => 'Hide',
    'new_version_available' => 'A new version of :releases_link is available. You\'re running v:user_version, the latest is v:latest_version',
    'invoice_settings' => 'Invoice Settings',
    'invoice_number_prefix' => 'Invoice Number Prefix',
    'invoice_number_counter' => 'Invoice Number Counter',
    'quote_number_prefix' => 'Quote Number Prefix',
    'quote_number_counter' => 'Quote Number Counter',
    'share_invoice_counter' => 'Share invoice counter',
    'invoice_issued_to' => 'Invoice issued to',
    'invalid_counter' => 'To prevent a possible conflict please set either an invoice or quote number prefix',
    'mark_sent' => 'Mark Sent',
    'more_designs' => 'More designs',
    'more_designs_title' => 'Additional Invoice Designs',
    'more_designs_cloud_header' => 'Go Pro for more invoice designs',
    'more_designs_cloud_text' => '',
    'more_designs_self_host_text' => '',
    'buy' => 'Buy',
    'bought_designs' => 'Successfully added additional invoice designs',
    'sent' => 'Sent',
    'vat_number' => 'VAT Number',
    'payment_title' => 'Enter Your Billing Address and Credit Card information',
    'payment_cvv' => '*This is the 3-4 digit number on the back of your card',
    'payment_footer1' => '*Billing address must match address associated with credit card.',
    'payment_footer2' => '*Please click "PAY NOW" only once - transaction may take up to 1 minute to process.',
    'id_number' => 'ID Number',
    'white_label_link' => 'White label',
    'white_label_header' => 'White Label',
    'bought_white_label' => 'Successfully enabled white label license',
    'white_labeled' => 'White labeled',
    'restore' => 'Restore',
    'restore_invoice' => 'Restore Invoice',
    'restore_quote' => 'Restore Quote',
    'restore_client' => 'Restore Client',
    'restore_credit' => 'Restore Credit',
    'restore_payment' => 'Restore Payment',
    'restored_invoice' => 'Successfully restored invoice',
    'restored_quote' => 'Successfully restored quote',
    'restored_client' => 'Successfully restored client',
    'restored_payment' => 'Successfully restored payment',
    'restored_credit' => 'Successfully restored credit',
    'reason_for_canceling' => 'Help us improve our site by telling us why you\'re leaving.',
    'discount_percent' => 'Percent',
    'discount_amount' => 'Amount',
    'invoice_history' => 'Invoice History',
    'quote_history' => 'Quote History',
    'current_version' => 'Current version',
    'select_version' => 'Select version',
    'view_history' => 'View History',
    'edit_payment' => 'Edit Payment',
    'updated_payment' => 'Successfully updated payment',
    'deleted' => 'Deleted',
    'restore_user' => 'Restore User',
    'restored_user' => 'Successfully restored user',
    'show_deleted_users' => 'Show deleted users',
    'email_templates' => 'Email Templates',
    'invoice_email' => 'Invoice Email',
    'payment_email' => 'Payment Email',
    'quote_email' => 'Quote Email',
    'reset_all' => 'Reset All',
    'approve' => 'Approve',
    'token_billing_type_id' => 'Token Billing',
    'token_billing_1' => 'Disabled',
    'token_billing_2' => 'Opt-in - checkbox is shown but not selected',
    'token_billing_3' => 'Opt-out - checkbox is shown and selected',
    'token_billing_4' => 'Always',
    'token_billing_checkbox' => 'Store credit card details',
    'view_in_gateway' => 'View in :gateway',
    'use_card_on_file' => 'Use Card on File',
    'edit_payment_details' => 'Edit payment details',
    'token_billing' => 'Save card details',
    'token_billing_secure' => 'The data is stored securely by :link',
    'support' => 'Support',
    'contact_information' => 'Contact Information',
    '256_encryption' => '256-Bit Encryption',
    'amount_due' => 'Amount due',
    'billing_address' => 'Billing Address',
    'billing_method' => 'Billing Method',
    'order_overview' => 'Order overview',
    'match_address' => '*Address must match address associated with credit card.',
    'click_once' => '*Please click "PAY NOW" only once - transaction may take up to 1 minute to process.',
    'invoice_footer' => 'Invoice Footer',
    'save_as_default_footer' => 'Save as default footer',
    'token_management' => 'Token Management',
    'tokens' => 'Tokens',
    'add_token' => 'Add Token',
    'show_deleted_tokens' => 'Show deleted tokens',
    'deleted_token' => 'Successfully deleted token',
    'created_token' => 'Successfully created token',
    'updated_token' => 'Successfully updated token',
    'edit_token' => 'Edit Token',
    'delete_token' => 'Delete Token',
    'token' => 'Token',
    'add_gateway' => 'Add Payment Gateway',
    'delete_gateway' => 'Delete Payment Gateway',
    'edit_gateway' => 'Edit Payment Gateway',
    'updated_gateway' => 'Successfully updated gateway',
    'created_gateway' => 'Successfully created gateway',
    'deleted_gateway' => 'Successfully deleted gateway',
    'pay_with_paypal' => 'PayPal',
    'pay_with_card' => 'Credit Card',
    'change_password' => 'Change password',
    'current_password' => 'Current password',
    'new_password' => 'New password',
    'confirm_password' => 'Confirm password',
    'password_error_incorrect' => 'The current password is incorrect.',
    'password_error_invalid' => 'The new password is invalid.',
    'updated_password' => 'Successfully updated password',
    'api_tokens' => 'API Tokens',
    'users_and_tokens' => 'Users & Tokens',
    'account_login' => 'Account Login',
    'recover_password' => 'Recover your password',
    'forgot_password' => 'Forgot your password?',
    'email_address' => 'Email address',
    'lets_go' => 'Let\'s go',
    'password_recovery' => 'Password Recovery',
    'send_email' => 'Send Email',
    'set_password' => 'Set Password',
    'converted' => 'Converted',
    'email_approved' => 'Email me when a quote is <b>approved</b>',
    'notification_quote_approved_subject' => 'Quote :invoice was approved by :client',
    'notification_quote_approved' => 'The following client :client approved Quote :invoice for :amount.',
    'resend_confirmation' => 'Resend confirmation email',
    'confirmation_resent' => 'The confirmation email was resent',
    'payment_type_credit_card' => 'Credit Card',
    'payment_type_paypal' => 'PayPal',
    'payment_type_bitcoin' => 'Bitcoin',
    'payment_type_gocardless' => 'GoCardless',
    'knowledge_base' => 'Knowledge Base',
    'partial' => 'Partial/Deposit',
    'partial_remaining' => ':partial of :balance',
    'more_fields' => 'More Fields',
    'less_fields' => 'Less Fields',
    'client_name' => 'Client Name',
    'pdf_settings' => 'PDF Settings',
    'product_settings' => 'Product Settings',
    'auto_wrap' => 'Auto Line Wrap',
    'duplicate_post' => 'Warning: the previous page was submitted twice. The second submission had been ignored.',
    'view_documentation' => 'View Documentation',
    'app_title' => 'Complete Business Management',
    'app_description' => 'Ozoo ERP is a comprehensive business management solution for invoicing, billing, and customer management. With Ozoo ERP, you can easily build and send professional invoices from any device that has access to the web. Your clients can print your invoices, download them as pdf files, and even pay you online from within the system.',
    'rows' => 'rows',
    'www' => 'www',
    'logo' => 'Logo',
    'subdomain' => 'Subdomain',
    'provide_name_or_email' => 'Please provide a name or email',
    'charts_and_reports' => 'Charts & Reports',
    'chart' => 'Chart',
    'report' => 'Report',
    'group_by' => 'Group by',
    'paid' => 'Paid',
    'enable_report' => 'Report',
    'enable_chart' => 'Chart',
    'totals' => 'Totals',
    'run' => 'Run',
    'export' => 'Export',
    'documentation' => 'Documentation',
    'zapier' => 'Zapier',
    'recurring' => 'Recurring',
    'last_invoice_sent' => 'Last invoice sent :date',
    'processed_updates' => 'Successfully completed update',
    'tasks' => 'Tasks',
    'new_task' => 'New Task',
    'start_time' => 'Start Time',
    'created_task' => 'Successfully created task',
    'updated_task' => 'Successfully updated task',
    'edit_task' => 'Edit Task',
    'clone_task' => 'Clone Task',
    'archive_task' => 'Archive Task',
    'restore_task' => 'Restore Task',
    'delete_task' => 'Delete Task',
    'stop_task' => 'Stop Task',
    'time' => 'Time',
    'start' => 'Start',
    'stop' => 'Stop',
    'now' => 'Now',
    'timer' => 'Timer',
    'manual' => 'Manual',
    'date_and_time' => 'Date & Time',
    'second' => 'Second',
    'seconds' => 'Seconds',
    'minute' => 'Minute',
    'minutes' => 'Minutes',
    'hour' => 'Hour',
    'hours' => 'Hours',
    'task_details' => 'Task Details',
    'duration' => 'Duration',
    'time_log' => 'Time Log',
    'end_time' => 'End Time',
    'end' => 'End',
    'invoiced' => 'Invoiced',
    'logged' => 'Logged',
    'running' => 'Running',
    'task_error_multiple_clients' => 'The tasks can\'t belong to different clients',
    'task_error_running' => 'Please stop running tasks first',
    'task_error_invoiced' => 'Tasks have already been invoiced',
    'restored_task' => 'Successfully restored task',
    'archived_task' => 'Successfully archived task',
    'archived_tasks' => 'Successfully archived :count tasks',
    'deleted_task' => 'Successfully deleted task',
    'deleted_tasks' => 'Successfully deleted :count tasks',
    'create_task' => 'Create Task',
    'stopped_task' => 'Successfully stopped task',
    'invoice_task' => 'Invoice Task',
    'invoice_labels' => 'Invoice Labels',
    'prefix' => 'Prefix',
    'counter' => 'Counter',
    'payment_type_dwolla' => 'Dwolla',
    'partial_value' => 'Must be greater than zero and less than the total',
    'more_actions' => 'More Actions',
    'pro_plan_title' => 'NINJA PRO',
    'pro_plan_call_to_action' => 'Upgrade Now!',
    'pro_plan_feature1' => 'Create Unlimited Clients',
    'pro_plan_feature2' => 'Access to 10 Beautiful Invoice Designs',
    'pro_plan_feature3' => 'Custom URLs - "YourBrand.InvoiceNinja.com"',
    'pro_plan_feature4' => 'Remove "Created by Invoice Ninja"',
    'pro_plan_feature5' => 'Multi-user Access & Activity Tracking',
    'pro_plan_feature6' => 'Create Quotes & Pro-forma Invoices',
    'pro_plan_feature7' => 'Customize Invoice Field Titles & Numbering',
    'pro_plan_feature8' => 'Option to attach PDFs to Client Emails',
    'resume' => 'Resume',
    'break_duration' => 'Break',
    'edit_details' => 'Edit Details',
    'work' => 'Work',
    'timezone_unset' => 'Please :link to set your timezone',
    'click_here' => 'click here',
    'email_receipt' => 'Email payment receipt to the client',
    'created_payment_emailed_client' => 'Successfully created payment and emailed client',
    'add_company' => 'Add Company',
    'untitled' => 'Untitled',
    'new_company' => 'New Company',
    'associated_accounts' => 'Successfully linked accounts',
    'unlinked_account' => 'Successfully unlinked accounts',
    'login' => 'Login',
    'or' => 'or',
    'email_error' => 'There was a problem sending the email',
    'confirm_recurring_timing' => 'Note: emails are sent at the start of the hour.',
    'confirm_recurring_timing_not_sent' => 'Note: invoices are created at the start of the hour.',
    'unlink_account' => 'Unlink Account',
    'unlink' => 'Unlink',
    'show_address' => 'Show Address',
    'show_address_help' => 'Require client to provide their billing address',
    'update_address' => 'Update Address',
    'update_address_help' => 'Update client\'s address with provided details',
    'times' => 'Times',
    'set_now' => 'Set to now',
    'dark_mode' => 'Dark Mode',
    'dark_mode_help' => 'Use a dark background for the sidebars',
    'add_to_invoice' => 'Add to invoice :invoice',
    'create_new_invoice' => 'Create new invoice',
    'task_errors' => 'Please correct any overlapping times',
    'from' => 'From',
    'to' => 'To',
    'font_size' => 'Font Size',
    'primary_color' => 'Primary Color',
    'secondary_color' => 'Secondary Color',
    'customize_design' => 'Customize Design',
    'content' => 'Content',
    'styles' => 'Styles',
    'defaults' => 'Defaults',
    'margins' => 'Margins',
    'header' => 'Header',
    'footer' => 'Footer',
    'custom' => 'Custom',
    'invoice_to' => 'Invoice to',
    'invoice_no' => 'Invoice No.',
    'quote_no' => 'Quote No.',
    'recent_payments' => 'Recent Payments',
    'outstanding' => 'Outstanding',
    'manage_companies' => 'Manage Companies',
    'total_revenue' => 'Total Revenue',
    'current_user' => 'Current User',
    'new_recurring_invoice' => 'New Recurring Invoice',
    'recurring_invoice' => 'Recurring Invoice',
    'new_recurring_quote' => 'New Recurring Quote',
    'recurring_quote' => 'Recurring Quote',
    'created_by_invoice' => 'Created by :invoice',
    'primary_user' => 'Primary User',
    'help' => 'Help',
    'playground' => 'playground',
    'support_forum' => 'Support Forums',
    'invoice_due_date' => 'Due Date',
    'quote_due_date' => 'Valid Until',
    'valid_until' => 'Valid Until',
    'reset_terms' => 'Reset terms',
    'reset_footer' => 'Reset footer',
    'invoice_sent' => ':count invoice sent',
    'invoices_sent' => ':count invoices sent',
    'status_draft' => 'Draft',
    'status_sent' => 'Sent',
    'status_viewed' => 'Viewed',
    'status_partial' => 'Partial',
    'status_paid' => 'Paid',
    'status_unpaid' => 'Unpaid',
    'status_all' => 'All',
    'show_line_item_tax' => 'Display <b>line item taxes inline</b>',
    'auto_bill' => 'Auto Bill',
    'military_time' => '24 Hour Time',
    'last_sent' => 'Last Sent',
    'reminder_emails' => 'Reminder Emails',
    'quote_reminder_emails' => 'Quote Reminder Emails',
    'templates_and_reminders' => 'Templates & Reminders',
    'subject' => 'Subject',
    'body' => 'Body',
    'first_reminder' => 'First Reminder',
    'second_reminder' => 'Second Reminder',
    'third_reminder' => 'Third Reminder',
    'num_days_reminder' => 'Days after due date',
    'reminder_subject' => 'Reminder: Invoice :invoice from :account',
    'reset' => 'Reset',
    'invoice_not_found' => 'The requested invoice is not available',
    'referral_program' => 'Referral Program',
    'referral_code' => 'Referral URL',
    'last_sent_on' => 'Sent Last: :date',
    'page_expire' => 'This page will expire soon, :click_here to keep working',
    'upcoming_quotes' => 'Upcoming Quotes',
    'expired_quotes' => 'Expired Quotes',
    'sign_up_using' => 'Sign up using',
    'invalid_credentials' => 'These credentials do not match our records',
    'show_all_options' => 'Show all options',
    'user_details' => 'User Details',
    'oneclick_login' => 'Connected Account',
    'disable' => 'Disable',
    'invoice_quote_number' => 'Invoice and Quote Numbers',
    'invoice_charges' => 'Invoice Surcharges',
    'notification_invoice_bounced' => 'We were unable to deliver Invoice :invoice to :contact. <br><br> :error',
    'notification_invoice_bounced_subject' => 'Unable to deliver Invoice :invoice',
    'notification_quote_bounced' => 'We were unable to deliver Quote :invoice to :contact. <br><br> :error',
    'notification_quote_bounced_subject' => 'Unable to deliver Quote :invoice',
    'custom_invoice_link' => 'Custom Invoice Link',
    'total_invoiced' => 'Total Invoiced',
    'open_balance' => 'Open Balance',
    'verify_email' => 'Please visit the link in the account confirmation email to verify your email address.',
    'basic_settings' => 'Basic Settings',
    'pro' => 'Pro',
    'gateways' => 'Payment Gateways',
    'next_send_on' => 'Send Next: :date',
    'no_longer_running' => 'This invoice is not scheduled to run',
    'general_settings' => 'General Settings',
    'customize' => 'Customize',
    'oneclick_login_help' => 'Connect an account to login without a password',
    'referral_code_help' => 'Earn money by sharing our app online',
    'enable_with_stripe' => 'Enable | Requires Stripe',
    'tax_settings' => 'Tax Settings',
    'create_tax_rate' => 'Add Tax Rate',
    'updated_tax_rate' => 'Successfully updated tax rate',
    'created_tax_rate' => 'Successfully created tax rate',
    'edit_tax_rate' => 'Edit tax rate',
    'archive_tax_rate' => 'Archive Tax Rate',
    'archived_tax_rate' => 'Successfully archived the tax rate',
    'default_tax_rate_id' => 'Default Tax Rate',
    'tax_rate' => 'Tax Rate',
    'recurring_hour' => 'Recurring Hour',
    'pattern' => 'Pattern',
    'pattern_help_title' => 'Pattern Help',
    'pattern_help_1' => 'Create custom numbers by specifying a pattern',
    'pattern_help_2' => 'Available variables:',
    'pattern_help_3' => 'For example, :example would be converted to :value',
    'see_options' => 'See options',
    'invoice_counter' => 'Invoice Counter',
    'quote_counter' => 'Quote Counter',
    'type' => 'Type',
    'activity_1' => ':user created client :client',
    'activity_2' => ':user archived client :client',
    'activity_3' => ':user deleted client :client',
    'activity_4' => ':user created invoice :invoice',
    'activity_5' => ':user updated invoice :invoice',
    'activity_6' => ':user emailed invoice :invoice for :client to :contact',
    'activity_7' => ':contact viewed invoice :invoice for :client',
    'activity_8' => ':user archived invoice :invoice',
    'activity_9' => ':user deleted invoice :invoice',
    'activity_10' => ':user entered payment :payment for :payment_amount on invoice :invoice for :client',
    'activity_11' => ':user updated payment :payment',
    'activity_12' => ':user archived payment :payment',
    'activity_13' => ':user deleted payment :payment',
    'activity_14' => ':user entered :credit credit',
    'activity_15' => ':user updated :credit credit',
    'activity_16' => ':user archived :credit credit',
    'activity_17' => ':user deleted :credit credit',
    'activity_18' => ':user created quote :quote',
    'activity_19' => ':user updated quote :quote',
    'activity_20' => ':user emailed quote :quote for :client to :contact',
    'activity_21' => ':contact viewed quote :quote',
    'activity_22' => ':user archived quote :quote',
    'activity_23' => ':user deleted quote :quote',
    'activity_24' => ':user restored quote :quote',
    'activity_25' => ':user restored invoice :invoice',
    'activity_26' => ':user restored client :client',
    'activity_27' => ':user restored payment :payment',
    'activity_28' => ':user restored :credit credit',
    'activity_29' => ':contact approved quote :quote for :client',
    'activity_30' => ':user created vendor :vendor',
    'activity_31' => ':user archived vendor :vendor',
    'activity_32' => ':user deleted vendor :vendor',
    'activity_33' => ':user restored vendor :vendor',
    'activity_34' => ':user created expense :expense',
    'activity_35' => ':user archived expense :expense',
    'activity_36' => ':user deleted expense :expense',
    'activity_37' => ':user restored expense :expense',
    'activity_42' => ':user created task :task',
    'activity_43' => ':user updated task :task',
    'activity_44' => ':user archived task :task',
    'activity_45' => ':user deleted task :task',
    'activity_46' => ':user restored task :task',
    'activity_47' => ':user updated expense :expense',
    'activity_48' => ':user created user :user',
    'activity_49' => ':user updated user :user',
    'activity_50' => ':user archived user :user',
    'activity_51' => ':user deleted user :user',
    'activity_52' => ':user restored user :user',
    'activity_53' => ':user marked sent :invoice',
    'activity_54' => ':user paid invoice :invoice',
    'activity_55' => ':contact replied ticket :ticket',
    'activity_56' => ':user viewed ticket :ticket',

    'payment' => 'Payment',
    'system' => 'System',
    'signature' => 'Email Signature',
    'default_messages' => 'Default Messages',
    'quote_terms' => 'Quote Terms',
    'default_quote_terms' => 'Default Quote Terms',
    'default_invoice_terms' => 'Default Invoice Terms',
    'default_invoice_footer' => 'Default Invoice Footer',
    'quote_footer' => 'Quote Footer',
    'free' => 'Free',
    'quote_is_approved' => 'Successfully approved',
    'apply_credit' => 'Apply Credit',
    'system_settings' => 'System Settings',
    'archive_token' => 'Archive Token',
    'archived_token' => 'Successfully archived token',
    'archive_user' => 'Archive User',
    'archived_user' => 'Successfully archived user',
    'archive_account_gateway' => 'Delete Gateway',
    'archived_account_gateway' => 'Successfully archived gateway',
    'archive_recurring_invoice' => 'Archive Recurring Invoice',
    'archived_recurring_invoice' => 'Successfully archived recurring invoice',
    'delete_recurring_invoice' => 'Delete Recurring Invoice',
    'deleted_recurring_invoice' => 'Successfully deleted recurring invoice',
    'restore_recurring_invoice' => 'Restore Recurring Invoice',
    'restored_recurring_invoice' => 'Successfully restored recurring invoice',
    'archive_recurring_quote' => 'Archive Recurring Quote',
    'archived_recurring_quote' => 'Successfully archived recurring quote',
    'delete_recurring_quote' => 'Delete Recurring Quote',
    'deleted_recurring_quote' => 'Successfully deleted recurring quote',
    'restore_recurring_quote' => 'Restore Recurring Quote',
    'restored_recurring_quote' => 'Successfully restored recurring quote',
    'archived' => 'Archived',
    'untitled_account' => 'Untitled Company',
    'before' => 'Before',
    'after' => 'After',
    'reset_terms_help' => 'Reset to the default account terms',
    'reset_footer_help' => 'Reset to the default account footer',
    'export_data' => 'Export Data',
    'user' => 'User',
    'country' => 'Country',
    'include' => 'Include',
    'logo_too_large' => 'Your logo is :size, for better PDF performance we suggest uploading an image file less than 200KB',
    'import_freshbooks' => 'Import From FreshBooks',
    'import_data' => 'Import Data',
    'source' => 'Source',
    'csv' => 'CSV',
    'client_file' => 'Client File',
    'invoice_file' => 'Invoice File',
    'task_file' => 'Task File',
    'no_mapper' => 'No valid mapping for file',
    'invalid_csv_header' => 'Invalid CSV Header',
    'client_portal' => 'Client Portal',
    'admin' => 'Admin',
    'disabled' => 'Disabled',
    'show_archived_users' => 'Show archived users',
    'notes' => 'Notes',
    'invoice_will_create' => 'invoice will be created',
    'invoices_will_create' => 'invoices will be created',
    'failed_to_import' => 'The following records failed to import, they either already exist or are missing required fields.',
    'publishable_key' => 'Publishable Key',
    'secret_key' => 'Secret Key',
    'missing_publishable_key' => 'Set your Stripe publishable key for an improved checkout process',
    'email_design' => 'Email Design',
    'due_by' => 'Due by :date',
    'enable_email_markup' => 'Enable Markup',
    'enable_email_markup_help' => 'Make it easier for your clients to pay you by adding schema.org markup to your emails.',
    'template_help_title' => 'Templates Help',
    'template_help_1' => 'Available variables:',
    'email_design_id' => 'Email Style',
    'email_design_help' => 'Make your emails look more professional with HTML layouts.',
    'plain' => 'Plain',
    'light' => 'Light',
    'dark' => 'Dark',
    'industry_help' => 'Used to provide comparisons against the averages of companies of similar size and industry.',
    'subdomain_help' => 'Set the subdomain or display the invoice on your own website.',
    'website_help' => 'Display the invoice in an iFrame on your own website',
    'invoice_number_help' => 'Specify a prefix or use a custom pattern to dynamically set the invoice number.',
    'quote_number_help' => 'Specify a prefix or use a custom pattern to dynamically set the quote number.',
    'custom_client_fields_helps' => 'Add a field when creating a client and optionally display the label and value on the PDF.',
    'custom_account_fields_helps' => 'Add a label and value to the company details section of the PDF.',
    'custom_invoice_fields_helps' => 'Add a field when creating an invoice and optionally display the label and value on the PDF.',
    'custom_invoice_charges_helps' => 'Add a field when creating an invoice and include the charge in the invoice subtotals.',
    'token_expired' => 'Validation token was expired. Please try again.',
    'invoice_link' => 'Invoice Link',
    'button_confirmation_message' => 'Confirm your email.',
    'confirm' => 'Confirm',
    'email_preferences' => 'Email Preferences',
    'created_invoices' => 'Successfully created :count invoice(s)',
    'next_invoice_number' => 'The next invoice number is :number.',
    'next_quote_number' => 'The next quote number is :number.',
    'days_before' => 'days before the',
    'days_after' => 'days after the',
    'field_due_date' => 'due date',
    'field_invoice_date' => 'invoice date',
    'schedule' => 'Schedule',
    'email_designs' => 'Email Designs',
    'assigned_when_sent' => 'Assigned when sent',
    'white_label_purchase_link' => 'Purchase a white label license',
    'expense' => 'Expense',
    'expenses' => 'Expenses',
    'new_expense' => 'Enter Expense',
    'new_vendor' => 'New Vendor',
    'payment_terms_net' => 'Net',
    'vendor' => 'Vendor',
    'edit_vendor' => 'Edit Vendor',
    'archive_vendor' => 'Archive Vendor',
    'delete_vendor' => 'Delete Vendor',
    'view_vendor' => 'View Vendor',
    'deleted_expense' => 'Successfully deleted expense',
    'archived_expense' => 'Successfully archived expense',
    'deleted_expenses' => 'Successfully deleted expenses',
    'archived_expenses' => 'Successfully archived expenses',
    'expense_amount' => 'Expense Amount',
    'expense_balance' => 'Expense Balance',
    'expense_date' => 'Expense Date',
    'expense_should_be_invoiced' => 'Should this expense be invoiced?',
    'public_notes' => 'Public Notes',
    'invoice_amount' => 'Invoice Amount',
    'exchange_rate' => 'Exchange Rate',
    'yes' => 'Yes',
    'no' => 'No',
    'should_be_invoiced' => 'Should be invoiced',
    'view_expense' => 'View expense # :expense',
    'edit_expense' => 'Edit Expense',
    'archive_expense' => 'Archive Expense',
    'delete_expense' => 'Delete Expense',
    'view_expense_num' => 'Expense # :expense',
    'updated_expense' => 'Successfully updated expense',
    'created_expense' => 'Successfully created expense',
    'enter_expense' => 'Enter Expense',
    'view' => 'View',
    'restore_expense' => 'Restore Expense',
    'invoice_expense' => 'Invoice Expense',
    'expense_error_multiple_clients' => 'The expenses can\'t belong to different clients',
    'expense_error_invoiced' => 'Expense has already been invoiced',
    'convert_currency' => 'Convert currency',
    'num_days' => 'Number of Days',
    'create_payment_term' => 'Create Payment Term',
    'edit_payment_terms' => 'Edit Payment Term',
    'edit_payment_term' => 'Edit Payment Term',
    'archive_payment_term' => 'Archive Payment Term',
    'recurring_due_dates' => 'Recurring Invoice Due Dates',
    'recurring_due_date_help' => '<p>Automatically sets a due date for the invoice.</p>
        <p>Invoices on a monthly or yearly cycle set to be due on or before the day they are created will be due the next month. Invoices set to be due on the 29th or 30th in months that don\'t have that day will be due the last day of the month.</p>
        <p>Invoices on a weekly cycle set to be due on the day of the week they are created will be due the next week.</p>
        <p>For example:</p>
        <ul>
          <li>Today is the 15th, due date is 1st of the month. The due date should likely be the 1st of the next month.</li>
          <li>Today is the 15th, due date is the last day of the month. The due date will be the last day of the this month.
</li>
          <li>Today is the 15th, due date is the 15th day of the month. The due date will be the 15th day of <strong>next</strong> month.
</li>
          <li>Today is the Friday, due date is the 1st Friday after. The due date will be next Friday, not today.
</li>
        </ul>',
    'due' => 'Due',
    'next_due_on' => 'Due Next: :date',
    'use_client_terms' => 'Use client terms',
    'day_of_month' => ':ordinal day of month',
    'last_day_of_month' => 'Last day of month',
    'day_of_week_after' => ':ordinal :day after',
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    'header_font_id' => 'Header Font',
    'body_font_id' => 'Body Font',
    'color_font_help' => 'Note: the primary color and fonts are also used in the client portal and custom email designs.',
    'live_preview' => 'Live Preview',
    'invalid_mail_config' => 'Unable to send email, please check that the mail settings are correct.',
    'invoice_message_button' => 'To view your invoice for :amount, click the button below.',
    'quote_message_button' => 'To view your quote for :amount, click the button below.',
    'payment_message_button' => 'Thank you for your payment of :amount.',
    'payment_type_direct_debit' => 'Direct Debit',
    'bank_accounts' => 'Credit Cards & Banks',
    'add_bank_account' => 'Add Bank Account',
    'setup_account' => 'Setup Account',
    'import_expenses' => 'Import Expenses',
    'bank_id' => 'Bank',
    'integration_type' => 'Integration Type',
    'updated_bank_account' => 'Successfully updated bank account',
    'edit_bank_account' => 'Edit Bank Account',
    'archive_bank_account' => 'Archive Bank Account',
    'archived_bank_account' => 'Successfully archived bank account',
    'created_bank_account' => 'Successfully created bank account',
    'validate_bank_account' => 'Validate Bank Account',
    'bank_password_help' => 'Note: your password is transmitted securely and never stored on our servers.',
    'bank_password_warning' => 'Warning: your password may be transmitted in plain text, consider enabling HTTPS.',
    'username' => 'Username',
    'account_number' => 'Account Number',
    'account_name' => 'Account Name',
    'bank_account_error' => 'Failed to retrieve account details, please check your credentials.',
    'status_approved' => 'Approved',
    'quote_settings' => 'Quote Settings',
    'auto_convert_quote' => 'Auto Convert',
    'auto_convert_quote_help' => 'Automatically convert a quote to an invoice when approved.',
    'validate' => 'Validate',
    'info' => 'Info',
    'imported_expenses' => 'Successfully created :count_vendors vendor(s) and :count_expenses expense(s)',
    'iframe_url_help3' => 'Note: if you plan on accepting credit cards details we strongly recommend enabling HTTPS on your site.',
    'expense_error_multiple_currencies' => 'The expenses can\'t have different currencies.',
    'expense_error_mismatch_currencies' => 'The client\'s currency does not match the expense currency.',
    'trello_roadmap' => 'Trello Roadmap',
    'header_footer' => 'Header/Footer',
    'first_page' => 'First page',
    'all_pages' => 'All pages',
    'last_page' => 'Last page',
    'all_pages_header' => 'Show Header on',
    'all_pages_footer' => 'Show Footer on',
    'invoice_currency' => 'Invoice Currency',
    'enable_https' => 'We strongly recommend using HTTPS to accept credit card details online.',
    'quote_issued_to' => 'Quote issued to',
    'show_currency_code' => 'Currency Code',
    'free_year_message' => 'Your account has been upgraded to the pro plan for one year at no cost.',
    'trial_message' => 'Your account will receive a free two week trial of our pro plan.',
    'trial_footer' => 'Your free pro plan trial lasts :count more days, :link to upgrade now.',
    'trial_footer_last_day' => 'This is the last day of your free pro plan trial, :link to upgrade now.',
    'trial_call_to_action' => 'Start Free Trial',
    'trial_success' => 'Successfully enabled two week free pro plan trial',
    'overdue' => 'Overdue',
    'white_label_text' => 'Purchase a ONE YEAR white label license for $:price to remove the Invoice Ninja branding from the invoice and client portal.',
    'user_email_footer' => 'To adjust your email notification settings please visit :link',
    'reset_password_footer' => 'If you did not request this password reset please email our support: :email',
    'limit_users' => 'Sorry, this will exceed the limit of :limit users',
    'more_designs_self_host_header' => 'Get 6 more invoice designs for just $:price',
    'old_browser' => 'Please use a :link',
    'newer_browser' => 'newer browser',
    'white_label_custom_css' => ':link for $:price to enable custom styling and help support our project.',
    'pro_plan_remove_logo' => ':link to remove the Invoice Ninja logo by joining the Pro Plan',
    'pro_plan_remove_logo_link' => 'Click here',
    'invitation_status_sent' => 'Sent',
    'invitation_status_opened' => 'Opened',
    'invitation_status_viewed' => 'Viewed',
    'email_error_inactive_client' => 'Emails can not be sent to inactive clients',
    'email_error_inactive_contact' => 'Emails can not be sent to inactive contacts',
    'email_error_inactive_invoice' => 'Emails can not be sent to inactive invoices',
    'email_error_inactive_proposal' => 'Emails can not be sent to inactive proposals',
    'email_error_user_unregistered' => 'Please register your account to send emails',
    'email_error_user_unconfirmed' => 'Please confirm your account to send emails',
    'email_error_invalid_contact_email' => 'Invalid contact email',
    'navigation' => 'Navigation',
    'list_invoices' => 'List Invoices',
    'list_clients' => 'List Clients',
    'list_quotes' => 'List Quotes',
    'list_tasks' => 'List Tasks',
    'list_expenses' => 'List Expenses',
    'list_recurring_invoices' => 'List Recurring Invoices',
    'list_payments' => 'List Payments',
    'list_credits' => 'List Credits',
    'tax_name' => 'Tax Name',
    'report_settings' => 'Report Settings',
    'new_user' => 'New User',
    'new_product' => 'New Product',
    'new_tax_rate' => 'New Tax Rate',
    'invoiced_amount' => 'Invoiced Amount',
    'invoice_item_fields' => 'Invoice Item Fields',
    'custom_invoice_item_fields_help' => 'Add a field when creating an invoice item and display the label and value on the PDF.',
    'recurring_invoice_number' => 'Recurring Number',
    'recurring_invoice_number_prefix_help' => 'Specify a prefix to be added to the invoice number for recurring invoices.',

    // Client Passwords
    'enable_portal_password' => 'Password Protect Invoices',
    'enable_portal_password_help' => 'Allows you to set a password for each contact. If a password is set, the contact will be required to enter a password before viewing invoices.',
    'send_portal_password' => 'Generate Automatically',
    'send_portal_password_help' => 'If no password is set, one will be generated and sent with the first invoice.',

    'expired' => 'Expired',
    'invalid_card_number' => 'The credit card number is not valid.',
    'invalid_expiry' => 'The expiration date is not valid.',
    'invalid_cvv' => 'The CVV is not valid.',
    'cost' => 'Cost',
    'create_invoice_for_sample' => 'Note: create your first invoice to see a preview here.',

    // User Permissions
    'owner' => 'Owner',
    'administrator' => 'Administrator',
    'administrator_help' => 'Allow user to manage users, change settings and modify all records',
    'user_create_all' => 'Create clients, invoices, etc.',
    'user_view_all' => 'View all clients, invoices, etc.',
    'user_edit_all' => 'Edit all clients, invoices, etc.',
    'partial_due' => 'Partial Due',
    'restore_vendor' => 'Restore Vendor',
    'restored_vendor' => 'Successfully restored vendor',
    'restored_expense' => 'Successfully restored expense',
    'permissions' => 'Permissions',
    'create_all_help' => 'Allow user to create and modify records',
    'view_all_help' => 'Allow user to view records they didn\'t create',
    'edit_all_help' => 'Allow user to modify records they didn\'t create',
    'view_payment' => 'View Payment',

    'january' => 'January',
    'february' => 'February',
    'march' => 'March',
    'april' => 'April',
    'may' => 'May',
    'june' => 'June',
    'july' => 'July',
    'august' => 'August',
    'september' => 'September',
    'october' => 'October',
    'november' => 'November',
    'december' => 'December',

    // Documents
    'documents_header' => 'Documents:',
    'email_documents_header' => 'Documents:',
    'email_documents_example_1' => 'Widgets Receipt.pdf',
    'email_documents_example_2' => 'Final Deliverable.zip',
    'quote_documents' => 'Quote Documents',
    'invoice_documents' => 'Invoice Documents',
    'expense_documents' => 'Expense Documents',
    'invoice_embed_documents' => 'Embed Images/Documents',
    'invoice_embed_documents_help' => 'Include attached images/pdfs in the invoice.',
    'document_email_attachment' => 'Attach Documents',
    'ubl_email_attachment' => 'Attach UBL',
    'download_documents' => 'Download Documents (:size)',
    'documents_from_expenses' => 'From Expenses:',
    'dropzone_default_message' => 'Drop files or click to upload',
    'dropzone_default_message_disabled' => 'Uploads disabled',
    'dropzone_fallback_message' => 'Your browser does not support drag\'n\'drop file uploads.',
    'dropzone_fallback_text' => 'Please use the fallback form below to upload your files like in the olden days.',
    'dropzone_file_too_big' => 'File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.',
    'dropzone_invalid_file_type' => 'You can\'t upload files of this type.',
    'dropzone_response_error' => 'Server responded with {{statusCode}} code.',
    'dropzone_cancel_upload' => 'Cancel upload',
    'dropzone_cancel_upload_confirmation' => 'Are you sure you want to cancel this upload?',
    'dropzone_remove_file' => 'Remove file',
    'documents' => 'Documents',
    'document_date' => 'Document Date',
    'document_size' => 'Size',

    'enable_client_portal' => 'Client Portal',
    'enable_client_portal_help' => 'Show/hide the client portal.',
    'enable_client_portal_dashboard' => 'Dashboard',
    'enable_client_portal_dashboard_help' => 'Show/hide the dashboard page in the client portal.',

    // Plans
    'account_management' => 'Account Management',
    'plan_status' => 'Plan Status',

    'plan_upgrade' => 'Upgrade',
    'plan_change' => 'Manage Plan',
    'pending_change_to' => 'Changes To',
    'plan_changes_to' => ':plan on :date',
    'plan_term_changes_to' => ':plan (:term) on :date',
    'cancel_plan_change' => 'Cancel Change',
    'plan' => 'Plan',
    'expires' => 'Expires',
    'renews' => 'Renews',
    'plan_expired' => ':plan Plan Expired',
    'trial_expired' => ':plan Plan Trial Ended',
    'never' => 'Never',
    'plan_free' => 'Free',
    'plan_pro' => 'Pro',
    'plan_enterprise' => 'Enterprise',
    'plan_white_label' => 'Self Hosted (White labeled)',
    'plan_free_self_hosted' => 'Self Hosted (Free)',
    'plan_trial' => 'Trial',
    'plan_term' => 'Term',
    'plan_term_monthly' => 'Monthly',
    'plan_term_yearly' => 'Yearly',
    'plan_term_month' => 'Month',
    'plan_term_year' => 'Year',
    'plan_price_monthly' => '$:price/Month',
    'plan_price_yearly' => '$:price/Year',
    'updated_plan' => 'Updated plan settings',
    'plan_paid' => 'Term Started',
    'plan_started' => 'Plan Started',
    'plan_expires' => 'Plan Expires',

    'white_label_button' => 'Purchase White Label',

    'pro_plan_year_description' => 'One year enrollment in the Invoice Ninja Pro Plan.',
    'pro_plan_month_description' => 'One month enrollment in the Invoice Ninja Pro Plan.',
    'enterprise_plan_product' => 'Enterprise Plan',
    'enterprise_plan_year_description' => 'One year enrollment in the Invoice Ninja Enterprise Plan.',
    'enterprise_plan_month_description' => 'One month enrollment in the Invoice Ninja Enterprise Plan.',
    'plan_credit_product' => 'Credit',
    'plan_credit_description' => 'Credit for unused time',
    'plan_pending_monthly' => 'Will switch to monthly on :date',
    'plan_refunded' => 'A refund has been issued.',

    'page_size' => 'Page Size',
    'live_preview_disabled' => 'Live preview has been disabled to support selected font',
    'invoice_number_padding' => 'Padding',
    'preview' => 'Preview',
    'list_vendors' => 'List Vendors',
    'add_users_not_supported' => 'Upgrade to the Enterprise Plan to add additional users to your account.',
    'enterprise_plan_features' => 'The Enterprise Plan adds support for multiple users and file attachments, :link to see the full list of features.',
    'return_to_app' => 'Return To App',


    // Payment updates
    'refund_payment' => 'Refund Payment',
    'refund_max' => 'Max:',
    'refund' => 'Refund',
    'are_you_sure_refund' => 'Refund selected payments?',
    'status_pending' => 'Pending',
    'status_completed' => 'Completed',
    'status_failed' => 'Failed',
    'status_partially_refunded' => 'Partially Refunded',
    'status_partially_refunded_amount' => ':amount Refunded',
    'status_refunded' => 'Refunded',
    'status_voided' => 'Cancelled',
    'refunded_payment' => 'Refunded Payment',
    'activity_39' => ':user cancelled a :payment_amount payment :payment',
    'activity_40' => ':user refunded :adjustment of a :payment_amount payment :payment',
    'card_expiration' => 'Exp:&nbsp:expires',

    'card_creditcardother' => 'Unknown',
    'card_americanexpress' => 'American Express',
    'card_carteblanche' => 'Carte Blanche',
    'card_unionpay' => 'UnionPay',
    'card_diners' => 'Diners Club',
    'card_discover' => 'Discover',
    'card_jcb' => 'JCB',
    'card_laser' => 'Laser',
    'card_maestro' => 'Maestro',
    'card_mastercard' => 'MasterCard',
    'card_solo' => 'Solo',
    'card_switch' => 'Switch',
    'card_visacard' => 'Visa',
    'card_ach' => 'ACH',

    'payment_type_stripe' => 'Stripe',
    'ach' => 'ACH',
    'enable_ach' => 'Accept US bank transfers',
    'stripe_ach_help' => 'ACH support must also be enabled in :link.',
    'ach_disabled' => 'Another gateway is already configured for direct debit.',

    'plaid' => 'Plaid',
    'client_id' => 'Client Id',
    'secret' => 'Secret',
    'public_key' => 'Public Key',
    'plaid_optional' => '(optional)',
    'plaid_environment_help' => 'When a Stripe test key is given, Plaid\'s development environment (tartan) will be used.',
    'other_providers' => 'Other Providers',
    'country_not_supported' => 'That country is not supported.',
    'invalid_routing_number' => 'The routing number is not valid.',
    'invalid_account_number' => 'The account number is not valid.',
    'account_number_mismatch' => 'The account numbers do not match.',
    'missing_account_holder_type' => 'Please select an individual or company account.',
    'missing_account_holder_name' => 'Please enter the account holder\'s name.',
    'routing_number' => 'Routing Number',
    'confirm_account_number' => 'Confirm Account Number',
    'individual_account' => 'Individual Account',
    'company_account' => 'Company Account',
    'account_holder_name' => 'Account Holder Name',
    'add_account' => 'Add Account',
    'payment_methods' => 'Payment Methods',
    'complete_verification' => 'Complete Verification',
    'verification_amount1' => 'Amount 1',
    'verification_amount2' => 'Amount 2',
    'payment_method_verified' => 'Verification completed successfully',
    'verification_failed' => 'Verification Failed',
    'remove_payment_method' => 'Remove Payment Method',
    'confirm_remove_payment_method' => 'Are you sure you want to remove this payment method?',
    'remove' => 'Remove',
    'payment_method_removed' => 'Removed payment method.',
    'bank_account_verification_help' => 'We have made two deposits into your account with the description "VERIFICATION". These deposits will take 1-2 business days to appear on your statement. Please enter the amounts below.',
    'bank_account_verification_next_steps' => 'We have made two deposits into your account with the description "VERIFICATION". These deposits will take 1-2 business days to appear on your statement.
        Once you have the amounts, come back to this payment methods page and click "Complete Verification" next to the account.',
    'unknown_bank' => 'Unknown Bank',
    'ach_verification_delay_help' => 'You will be able to use the account after completing verification. Verification usually takes 1-2 business days.',
    'add_credit_card' => 'Add Credit Card',
    'payment_method_added' => 'Added payment method.',
    'use_for_auto_bill' => 'Use For Autobill',
    'used_for_auto_bill' => 'Autobill Payment Method',
    'payment_method_set_as_default' => 'Set Autobill payment method.',
    'activity_41' => ':payment_amount payment (:payment) failed',
    'webhook_url' => 'Webhook URL',
    'stripe_webhook_help' => 'You must :link.',
    'stripe_webhook_help_link_text' => 'add this URL as an endpoint at Stripe',
    'gocardless_webhook_help_link_text' => 'add this URL as an endpoint in GoCardless',
    'payment_method_error' => 'There was an error adding your payment methd. Please try again later.',
    'notification_invoice_payment_failed_subject' => 'Payment failed for Invoice :invoice',
    'notification_invoice_payment_failed' => 'A payment made by client :client towards Invoice :invoice failed. The payment has been marked as failed and :amount has been added to the client\'s balance.',
    'link_with_plaid' => 'Link Account Instantly with Plaid',
    'link_manually' => 'Link Manually',
    'secured_by_plaid' => 'Secured by Plaid',
    'plaid_linked_status' => 'Your bank account at :bank',
    'add_payment_method' => 'Add Payment Method',
    'account_holder_type' => 'Account Holder Type',
    'ach_authorization' => 'I authorize :company to use my bank account for future payments and, if necessary, electronically credit my account to correct erroneous debits. I understand that I may cancel this authorization at any time by removing the payment method or by contacting :email.',
    'ach_authorization_required' => 'You must consent to ACH transactions.',
    'off' => 'Off',
    'opt_in' => 'Opt-in',
    'opt_out' => 'Opt-out',
    'always' => 'Always',
    'opted_out' => 'Opted out',
    'opted_in' => 'Opted in',
    'manage_auto_bill' => 'Manage Auto-bill',
    'enabled' => 'Enabled',
    'paypal' => 'PayPal',
    'braintree_enable_paypal' => 'Enable PayPal payments through BrainTree',
    'braintree_paypal_disabled_help' => 'The PayPal gateway is processing PayPal payments',
    'braintree_paypal_help' => 'You must also :link.',
    'braintree_paypal_help_link_text' => 'link PayPal to your BrainTree account',
    'token_billing_braintree_paypal' => 'Save payment details',
    'add_paypal_account' => 'Add PayPal Account',
    'no_payment_method_specified' => 'No payment method specified',
    'chart_type' => 'Chart Type',
    'format' => 'Format',
    'import_ofx' => 'Import OFX',
    'ofx_file' => 'OFX File',
    'ofx_parse_failed' => 'Failed to parse OFX file',

    // WePay
    'wepay' => 'WePay',
    'sign_up_with_wepay' => 'Sign up with WePay',
    'use_another_provider' => 'Use another provider',
    'company_name' => 'Company Name',
    'wepay_company_name_help' => 'This will appear on client\'s credit card statements.',
    'wepay_description_help' => 'The purpose of this account.',
    'wepay_tos_agree' => 'I agree to the :link.',
    'wepay_tos_link_text' => 'WePay Terms of Service',
    'resend_confirmation_email' => 'Resend Confirmation Email',
    'manage_account' => 'Manage Account',
    'action_required' => 'Action Required',
    'finish_setup' => 'Finish Setup',
    'created_wepay_confirmation_required' => 'Please check your email and confirm your email address with WePay.',
    'switch_to_wepay' => 'Switch to WePay',
    'switch' => 'Switch',
    'restore_account_gateway' => 'Restore Gateway',
    'restored_account_gateway' => 'Successfully restored gateway',
    'united_states' => 'United States',
    'canada' => 'Canada',
    'accept_debit_cards' => 'Accept Debit Cards',
    'debit_cards' => 'Debit Cards',

    'warn_start_date_changed' => 'The next invoice will be sent on the new start date.',
    'warn_start_date_changed_not_sent' => 'The next invoice will be created on the new start date.',
    'original_start_date' => 'Original start date',
    'new_start_date' => 'New start date',
    'security' => 'Security',
    'see_whats_new' => 'See what\'s new in v:version',
    'wait_for_upload' => 'Please wait for the document upload to complete.',
    'upgrade_for_permissions' => 'Upgrade to our Enterprise Plan to enable permissions.',
    'enable_second_tax_rate' => 'Enable specifying a <b>second tax rate</b>',
    'payment_file' => 'Payment File',
    'expense_file' => 'Expense File',
    'product_file' => 'Product File',
    'import_products' => 'Import Products',
    'products_will_create' => 'products will be created',
    'product_key' => 'Product',
    'created_products' => 'Successfully created/updated :count product(s)',
    'export_help' => 'Use JSON if you plan to import the data into Invoice Ninja.<br/>The file includes clients, products, invoices, quotes and payments.',
    'selfhost_export_help' => '<br/>We recommend using mysqldump to create a full backup.',
    'JSON_file' => 'JSON File',

    'view_dashboard' => 'View Dashboard',
    'client_session_expired' => 'Session Expired',
    'client_session_expired_message' => 'Your session has expired. Please click the link in your email again.',

    'auto_bill_notification' => 'This invoice will automatically be billed to your :payment_method on file on :due_date.',
    'auto_bill_payment_method_bank_transfer' => 'bank account',
    'auto_bill_payment_method_credit_card' => 'credit card',
    'auto_bill_payment_method_paypal' => 'PayPal account',
    'auto_bill_notification_placeholder' => 'This invoice will automatically be billed to your credit card on file on the due date.',
    'payment_settings' => 'Payment Settings',

    'on_send_date' => 'On send date',
    'on_due_date' => 'On due date',
    'auto_bill_ach_date_help' => 'ACH will always auto bill on the due date.',
    'warn_change_auto_bill' => 'Due to NACHA rules, changes to this invoice may prevent ACH auto bill.',

    'bank_account' => 'Bank Account',
    'payment_processed_through_wepay' => 'ACH payments will be processed using WePay.',
    'privacy_policy' => 'Privacy Policy',
    'ach_email_prompt' => 'Please enter your email address:',
    'verification_pending' => 'Verification Pending',

    'update_font_cache' => 'Please force refresh the page to update the font cache.',
    'more_options' => 'More options',
    'credit_card' => 'Credit Card',
    'bank_transfer' => 'Bank Transfer',
    'no_transaction_reference' => 'We did not receive a payment transaction reference from the gateway.',
    'use_bank_on_file' => 'Use Bank on File',
    'auto_bill_email_message' => 'This invoice will automatically be billed to the payment method on file on the due date.',
    'bitcoin' => 'Bitcoin',
    'gocardless' => 'GoCardless',
    'added_on' => 'Added :date',
    'failed_remove_payment_method' => 'Failed to remove the payment method',
    'gateway_exists' => 'This gateway already exists',
    'manual_entry' => 'Manual entry',
    'start_of_week' => 'First Day of the Week',

    // Frequencies
    'freq_inactive' => 'Inactive',
    'freq_daily' => 'Daily',
    'freq_weekly' => 'Weekly',
    'freq_biweekly' => 'Biweekly',
    'freq_two_weeks' => 'Two weeks',
    'freq_four_weeks' => 'Four weeks',
    'freq_monthly' => 'Monthly',
    'freq_three_months' => 'Three months',
    'freq_four_months' => 'Four months',
    'freq_six_months' => 'Six months',
    'freq_annually' => 'Annually',
    'freq_two_years' => 'Two years',

    // Payment types
    'payment_type_Apply Credit' => 'Apply Credit',
    'payment_type_Bank Transfer' => 'Bank Transfer',
    'payment_type_Cash' => 'Cash',
    'payment_type_Debit' => 'Debit',
    'payment_type_ACH' => 'ACH',
    'payment_type_Visa Card' => 'Visa Card',
    'payment_type_MasterCard' => 'MasterCard',
    'payment_type_American Express' => 'American Express',
    'payment_type_Discover Card' => 'Discover Card',
    'payment_type_Diners Card' => 'Diners Card',
    'payment_type_EuroCard' => 'EuroCard',
    'payment_type_Nova' => 'Nova',
    'payment_type_Credit Card Other' => 'Credit Card Other',
    'payment_type_PayPal' => 'PayPal',
    'payment_type_Google Wallet' => 'Google Wallet',
    'payment_type_Check' => 'Check',
    'payment_type_Carte Blanche' => 'Carte Blanche',
    'payment_type_UnionPay' => 'UnionPay',
    'payment_type_JCB' => 'JCB',
    'payment_type_Laser' => 'Laser',
    'payment_type_Maestro' => 'Maestro',
    'payment_type_Solo' => 'Solo',
    'payment_type_Switch' => 'Switch',
    'payment_type_iZettle' => 'iZettle',
    'payment_type_Swish' => 'Swish',
    'payment_type_Alipay' => 'Alipay',
    'payment_type_Sofort' => 'Sofort',
    'payment_type_SEPA' => 'SEPA Direct Debit',
    'payment_type_Bitcoin' => 'Bitcoin',
    'payment_type_GoCardless' => 'GoCardless',
    'payment_type_Zelle' => 'Zelle',

    // Countries
    'country_Afghanistan' => 'Afghanistan',
    'country_Albania' => 'Albania',
    'country_Antarctica' => 'Antarctica',
    'country_Algeria' => 'Algeria',
    'country_American Samoa' => 'American Samoa',
    'country_Andorra' => 'Andorra',
    'country_Angola' => 'Angola',
    'country_Antigua and Barbuda' => 'Antigua and Barbuda',
    'country_Azerbaijan' => 'Azerbaijan',
    'country_Argentina' => 'Argentina',
    'country_Australia' => 'Australia',
    'country_Austria' => 'Austria',
    'country_Bahamas' => 'Bahamas',
    'country_Bahrain' => 'Bahrain',
    'country_Bangladesh' => 'Bangladesh',
    'country_Armenia' => 'Armenia',
    'country_Barbados' => 'Barbados',
    'country_Belgium' => 'Belgium',
    'country_Bermuda' => 'Bermuda',
    'country_Bhutan' => 'Bhutan',
    'country_Bolivia, Plurinational State of' => 'Bolivia, Plurinational State of',
    'country_Bosnia and Herzegovina' => 'Bosnia and Herzegovina',
    'country_Botswana' => 'Botswana',
    'country_Bouvet Island' => 'Bouvet Island',
    'country_Brazil' => 'Brazil',
    'country_Belize' => 'Belize',
    'country_British Indian Ocean Territory' => 'British Indian Ocean Territory',
    'country_Solomon Islands' => 'Solomon Islands',
    'country_Virgin Islands, British' => 'Virgin Islands, British',
    'country_Brunei Darussalam' => 'Brunei Darussalam',
    'country_Bulgaria' => 'Bulgaria',
    'country_Myanmar' => 'Myanmar',
    'country_Burundi' => 'Burundi',
    'country_Belarus' => 'Belarus',
    'country_Cambodia' => 'Cambodia',
    'country_Cameroon' => 'Cameroon',
    'country_Canada' => 'Canada',
    'country_Cape Verde' => 'Cape Verde',
    'country_Cayman Islands' => 'Cayman Islands',
    'country_Central African Republic' => 'Central African Republic',
    'country_Sri Lanka' => 'Sri Lanka',
    'country_Chad' => 'Chad',
    'country_Chile' => 'Chile',
    'country_China' => 'China',
    'country_Taiwan, Province of China' => 'Taiwan, Province of China',
    'country_Christmas Island' => 'Christmas Island',
    'country_Cocos (Keeling) Islands' => 'Cocos (Keeling) Islands',
    'country_Colombia' => 'Colombia',
    'country_Comoros' => 'Comoros',
    'country_Mayotte' => 'Mayotte',
    'country_Congo' => 'Congo',
    'country_Congo, the Democratic Republic of the' => 'Congo, the Democratic Republic of the',
    'country_Cook Islands' => 'Cook Islands',
    'country_Costa Rica' => 'Costa Rica',
    'country_Croatia' => 'Croatia',
    'country_Cuba' => 'Cuba',
    'country_Cyprus' => 'Cyprus',
    'country_Czech Republic' => 'Czech Republic',
    'country_Benin' => 'Benin',
    'country_Denmark' => 'Denmark',
    'country_Dominica' => 'Dominica',
    'country_Dominican Republic' => 'Dominican Republic',
    'country_Ecuador' => 'Ecuador',
    'country_El Salvador' => 'El Salvador',
    'country_Equatorial Guinea' => 'Equatorial Guinea',
    'country_Ethiopia' => 'Ethiopia',
    'country_Eritrea' => 'Eritrea',
    'country_Estonia' => 'Estonia',
    'country_Faroe Islands' => 'Faroe Islands',
    'country_Falkland Islands (Malvinas)' => 'Falkland Islands (Malvinas)',
    'country_South Georgia and the South Sandwich Islands' => 'South Georgia and the South Sandwich Islands',
    'country_Fiji' => 'Fiji',
    'country_Finland' => 'Finland',
    'country_Åland Islands' => 'Åland Islands',
    'country_France' => 'France',
    'country_French Guiana' => 'French Guiana',
    'country_French Polynesia' => 'French Polynesia',
    'country_French Southern Territories' => 'French Southern Territories',
    'country_Djibouti' => 'Djibouti',
    'country_Gabon' => 'Gabon',
    'country_Georgia' => 'Georgia',
    'country_Gambia' => 'Gambia',
    'country_Palestinian Territory, Occupied' => 'Palestinian Territory, Occupied',
    'country_Germany' => 'Germany',
    'country_Ghana' => 'Ghana',
    'country_Gibraltar' => 'Gibraltar',
    'country_Kiribati' => 'Kiribati',
    'country_Greece' => 'Greece',
    'country_Greenland' => 'Greenland',
    'country_Grenada' => 'Grenada',
    'country_Guadeloupe' => 'Guadeloupe',
    'country_Guam' => 'Guam',
    'country_Guatemala' => 'Guatemala',
    'country_Guinea' => 'Guinea',
    'country_Guyana' => 'Guyana',
    'country_Haiti' => 'Haiti',
    'country_Heard Island and McDonald Islands' => 'Heard Island and McDonald Islands',
    'country_Holy See (Vatican City State)' => 'Holy See (Vatican City State)',
    'country_Honduras' => 'Honduras',
    'country_Hong Kong' => 'Hong Kong',
    'country_Hungary' => 'Hungary',
    'country_Iceland' => 'Iceland',
    'country_India' => 'India',
    'country_Indonesia' => 'Indonesia',
    'country_Iran, Islamic Republic of' => 'Iran, Islamic Republic of',
    'country_Iraq' => 'Iraq',
    'country_Ireland' => 'Ireland',
    'country_Israel' => 'Israel',
    'country_Italy' => 'Italy',
    'country_Côte d\'Ivoire' => 'Côte d\'Ivoire',
    'country_Jamaica' => 'Jamaica',
    'country_Japan' => 'Japan',
    'country_Kazakhstan' => 'Kazakhstan',
    'country_Jordan' => 'Jordan',
    'country_Kenya' => 'Kenya',
    'country_Korea, Democratic People\'s Republic of' => 'Korea, Democratic People\'s Republic of',
    'country_Korea, Republic of' => 'Korea, Republic of',
    'country_Kuwait' => 'Kuwait',
    'country_Kyrgyzstan' => 'Kyrgyzstan',
    'country_Lao People\'s Democratic Republic' => 'Lao People\'s Democratic Republic',
    'country_Lebanon' => 'Lebanon',
    'country_Lesotho' => 'Lesotho',
    'country_Latvia' => 'Latvia',
    'country_Liberia' => 'Liberia',
    'country_Libya' => 'Libya',
    'country_Liechtenstein' => 'Liechtenstein',
    'country_Lithuania' => 'Lithuania',
    'country_Luxembourg' => 'Luxembourg',
    'country_Macao' => 'Macao',
    'country_Madagascar' => 'Madagascar',
    'country_Malawi' => 'Malawi',
    'country_Malaysia' => 'Malaysia',
    'country_Maldives' => 'Maldives',
    'country_Mali' => 'Mali',
    'country_Malta' => 'Malta',
    'country_Martinique' => 'Martinique',
    'country_Mauritania' => 'Mauritania',
    'country_Mauritius' => 'Mauritius',
    'country_Mexico' => 'Mexico',
    'country_Monaco' => 'Monaco',
    'country_Mongolia' => 'Mongolia',
    'country_Moldova, Republic of' => 'Moldova, Republic of',
    'country_Montenegro' => 'Montenegro',
    'country_Montserrat' => 'Montserrat',
    'country_Morocco' => 'Morocco',
    'country_Mozambique' => 'Mozambique',
    'country_Oman' => 'Oman',
    'country_Namibia' => 'Namibia',
    'country_Nauru' => 'Nauru',
    'country_Nepal' => 'Nepal',
    'country_Netherlands' => 'Netherlands',
    'country_Curaçao' => 'Curaçao',
    'country_Aruba' => 'Aruba',
    'country_Sint Maarten (Dutch part)' => 'Sint Maarten (Dutch part)',
    'country_Bonaire, Sint Eustatius and Saba' => 'Bonaire, Sint Eustatius and Saba',
    'country_New Caledonia' => 'New Caledonia',
    'country_Vanuatu' => 'Vanuatu',
    'country_New Zealand' => 'New Zealand',
    'country_Nicaragua' => 'Nicaragua',
    'country_Niger' => 'Niger',
    'country_Nigeria' => 'Nigeria',
    'country_Niue' => 'Niue',
    'country_Norfolk Island' => 'Norfolk Island',
    'country_Norway' => 'Norway',
    'country_Northern Mariana Islands' => 'Northern Mariana Islands',
    'country_United States Minor Outlying Islands' => 'United States Minor Outlying Islands',
    'country_Micronesia, Federated States of' => 'Micronesia, Federated States of',
    'country_Marshall Islands' => 'Marshall Islands',
    'country_Palau' => 'Palau',
    'country_Pakistan' => 'Pakistan',
    'country_Panama' => 'Panama',
    'country_Papua New Guinea' => 'Papua New Guinea',
    'country_Paraguay' => 'Paraguay',
    'country_Peru' => 'Peru',
    'country_Philippines' => 'Philippines',
    'country_Pitcairn' => 'Pitcairn',
    'country_Poland' => 'Poland',
    'country_Portugal' => 'Portugal',
    'country_Guinea-Bissau' => 'Guinea-Bissau',
    'country_Timor-Leste' => 'Timor-Leste',
    'country_Puerto Rico' => 'Puerto Rico',
    'country_Qatar' => 'Qatar',
    'country_Réunion' => 'Réunion',
    'country_Romania' => 'Romania',
    'country_Russian Federation' => 'Russian Federation',
    'country_Rwanda' => 'Rwanda',
    'country_Saint Barthélemy' => 'Saint Barthélemy',
    'country_Saint Helena, Ascension and Tristan da Cunha' => 'Saint Helena, Ascension and Tristan da Cunha',
    'country_Saint Kitts and Nevis' => 'Saint Kitts and Nevis',
    'country_Anguilla' => 'Anguilla',
    'country_Saint Lucia' => 'Saint Lucia',
    'country_Saint Martin (French part)' => 'Saint Martin (French part)',
    'country_Saint Pierre and Miquelon' => 'Saint Pierre and Miquelon',
    'country_Saint Vincent and the Grenadines' => 'Saint Vincent and the Grenadines',
    'country_San Marino' => 'San Marino',
    'country_Sao Tome and Principe' => 'Sao Tome and Principe',
    'country_Saudi Arabia' => 'Saudi Arabia',
    'country_Senegal' => 'Senegal',
    'country_Serbia' => 'Serbia',
    'country_Seychelles' => 'Seychelles',
    'country_Sierra Leone' => 'Sierra Leone',
    'country_Singapore' => 'Singapore',
    'country_Slovakia' => 'Slovakia',
    'country_Viet Nam' => 'Viet Nam',
    'country_Slovenia' => 'Slovenia',
    'country_Somalia' => 'Somalia',
    'country_South Africa' => 'South Africa',
    'country_Zimbabwe' => 'Zimbabwe',
    'country_Spain' => 'Spain',
    'country_South Sudan' => 'South Sudan',
    'country_Sudan' => 'Sudan',
    'country_Western Sahara' => 'Western Sahara',
    'country_Suriname' => 'Suriname',
    'country_Svalbard and Jan Mayen' => 'Svalbard and Jan Mayen',
    'country_Swaziland' => 'Swaziland',
    'country_Sweden' => 'Sweden',
    'country_Switzerland' => 'Switzerland',
    'country_Syrian Arab Republic' => 'Syrian Arab Republic',
    'country_Tajikistan' => 'Tajikistan',
    'country_Thailand' => 'Thailand',
    'country_Togo' => 'Togo',
    'country_Tokelau' => 'Tokelau',
    'country_Tonga' => 'Tonga',
    'country_Trinidad and Tobago' => 'Trinidad and Tobago',
    'country_United Arab Emirates' => 'United Arab Emirates',
    'country_Tunisia' => 'Tunisia',
    'country_Turkey' => 'Turkey',
    'country_Turkmenistan' => 'Turkmenistan',
    'country_Turks and Caicos Islands' => 'Turks and Caicos Islands',
    'country_Tuvalu' => 'Tuvalu',
    'country_Uganda' => 'Uganda',
    'country_Ukraine' => 'Ukraine',
    'country_Macedonia, the former Yugoslav Republic of' => 'Macedonia, the former Yugoslav Republic of',
    'country_Egypt' => 'Egypt',
    'country_United Kingdom' => 'United Kingdom',
    'country_Guernsey' => 'Guernsey',
    'country_Jersey' => 'Jersey',
    'country_Isle of Man' => 'Isle of Man',
    'country_Tanzania, United Republic of' => 'Tanzania, United Republic of',
    'country_United States' => 'United States',
    'country_Virgin Islands, U.S.' => 'Virgin Islands, U.S.',
    'country_Burkina Faso' => 'Burkina Faso',
    'country_Uruguay' => 'Uruguay',
    'country_Uzbekistan' => 'Uzbekistan',
    'country_Venezuela, Bolivarian Republic of' => 'Venezuela, Bolivarian Republic of',
    'country_Wallis and Futuna' => 'Wallis and Futuna',
    'country_Samoa' => 'Samoa',
    'country_Yemen' => 'Yemen',
    'country_Zambia' => 'Zambia',

    // Languages
    'lang_Brazilian Portuguese' => 'Brazilian Portuguese',
    'lang_Croatian' => 'Croatian',
    'lang_Czech' => 'Czech',
    'lang_Danish' => 'Danish',
    'lang_Dutch' => 'Dutch',
    'lang_English' => 'English',
    'lang_English - United States' => 'English',
    'lang_French' => 'French',
    'lang_French - Canada' => 'French - Canada',
    'lang_German' => 'German',
    'lang_Italian' => 'Italian',
    'lang_Japanese' => 'Japanese',
    'lang_Lithuanian' => 'Lithuanian',
    'lang_Norwegian' => 'Norwegian',
    'lang_Polish' => 'Polish',
    'lang_Spanish' => 'Spanish',
    'lang_Spanish - Spain' => 'Spanish - Spain',
    'lang_Swedish' => 'Swedish',
    'lang_Albanian' => 'Albanian',
    'lang_Greek' => 'Greek',
    'lang_English - United Kingdom' => 'English - United Kingdom',
    'lang_English - Australia' => 'English - Australia',
    'lang_Slovenian' => 'Slovenian',
    'lang_Finnish' => 'Finnish',
    'lang_Romanian' => 'Romanian',
    'lang_Turkish - Turkey' => 'Turkish - Turkey',
    'lang_Portuguese - Brazilian' => 'Portuguese - Brazilian',
    'lang_Portuguese - Portugal' => 'Portuguese - Portugal',
    'lang_Thai' => 'Thai',
    'lang_Macedonian' => 'Macedonian',
    'lang_Chinese - Taiwan' => 'Chinese - Taiwan',
    'lang_Serbian' => 'Serbian',
    'lang_Bulgarian' => 'Bulgarian',
    'lang_Russian (Russia)' => 'Russian (Russia)',


    // Industries
    'industry_Accounting & Legal' => 'Accounting & Legal',
    'industry_Advertising' => 'Advertising',
    'industry_Aerospace' => 'Aerospace',
    'industry_Agriculture' => 'Agriculture',
    'industry_Automotive' => 'Automotive',
    'industry_Banking & Finance' => 'Banking & Finance',
    'industry_Biotechnology' => 'Biotechnology',
    'industry_Broadcasting' => 'Broadcasting',
    'industry_Business Services' => 'Business Services',
    'industry_Commodities & Chemicals' => 'Commodities & Chemicals',
    'industry_Communications' => 'Communications',
    'industry_Computers & Hightech' => 'Computers & Hightech',
    'industry_Defense' => 'Defense',
    'industry_Energy' => 'Energy',
    'industry_Entertainment' => 'Entertainment',
    'industry_Government' => 'Government',
    'industry_Healthcare & Life Sciences' => 'Healthcare & Life Sciences',
    'industry_Insurance' => 'Insurance',
    'industry_Manufacturing' => 'Manufacturing',
    'industry_Marketing' => 'Marketing',
    'industry_Media' => 'Media',
    'industry_Nonprofit & Higher Ed' => 'Nonprofit & Higher Ed',
    'industry_Pharmaceuticals' => 'Pharmaceuticals',
    'industry_Professional Services & Consulting' => 'Professional Services & Consulting',
    'industry_Real Estate' => 'Real Estate',
    'industry_Retail & Wholesale' => 'Retail & Wholesale',
    'industry_Sports' => 'Sports',
    'industry_Transportation' => 'Transportation',
    'industry_Travel & Luxury' => 'Travel & Luxury',
    'industry_Other' => 'Other',
    'industry_Photography' => 'Photography',

    'view_client_portal' => 'View client portal',
    'view_portal' => 'View Portal',
    'vendor_contacts' => 'Vendor Contacts',
    'all' => 'All',
    'selected' => 'Selected',
    'category' => 'Category',
    'categories' => 'Categories',
    'new_expense_category' => 'New Expense Category',
    'edit_category' => 'Edit Category',
    'archive_expense_category' => 'Archive Category',
    'expense_categories' => 'Expense Categories',
    'list_expense_categories' => 'List Expense Categories',
    'updated_expense_category' => 'Successfully updated expense category',
    'created_expense_category' => 'Successfully created expense category',
    'archived_expense_category' => 'Successfully archived expense category',
    'archived_expense_categories' => 'Successfully archived :count expense category',
    'restore_expense_category' => 'Restore expense category',
    'restored_expense_category' => 'Successfully restored expense category',
    'apply_taxes' => 'Apply taxes',
    'min_to_max_users' => ':min to :max users',
    'max_users_reached' => 'The maximum number of users has been reached.',
    'buy_now_buttons' => 'Buy Now Buttons',
    'landing_page' => 'Landing Page',
    'payment_type' => 'Payment Type',
    'form' => 'Form',
    'link' => 'Link',
    'fields' => 'Fields',
    'dwolla' => 'Dwolla',
    'buy_now_buttons_warning' => 'Note: the client and invoice are created even if the transaction isn\'t completed.',
    'buy_now_buttons_disabled' => 'This feature requires that a product is created and a payment gateway is configured.',
    'enable_buy_now_buttons_help' => 'Enable support for buy now buttons',
    'changes_take_effect_immediately' => 'Note: changes take effect immediately',
    'wepay_account_description' => 'Payment gateway for Invoice Ninja',
    'payment_error_code' => 'There was an error processing your payment [:code]. Please try again later.',
    'standard_fees_apply' => 'Fee: 2.9%/1.2% [Credit Card/Bank Transfer] + $0.30 per successful charge.',
    'limit_import_rows' => 'Data needs to be imported in batches of :count rows or less',
    'error_title' => 'Something went wrong',
    'error_contact_text' => 'If you\'d like help please email us at :mailaddress',
    'no_undo' => 'Warning: this can\'t be undone.',
    'no_contact_selected' => 'Please select a contact',
    'no_client_selected' => 'Please select a client',

    'gateway_config_error' => 'It may help to set new passwords or generate new API keys.',
    'payment_type_on_file' => ':type on file',
    'invoice_for_client' => 'Invoice :invoice for :client',
    'intent_not_found' => 'Sorry, I\'m not sure what you\'re asking.',
    'intent_not_supported' => 'Sorry, I\'m not able to do that.',
    'client_not_found' => 'I wasn\'t able to find the client',
    'not_allowed' => 'Sorry, you don\'t have the needed permissions',
    'bot_emailed_invoice' => 'Your invoice has been sent.',
    'bot_emailed_notify_viewed' => 'I\'ll email you when it\'s viewed.',
    'bot_emailed_notify_paid' => 'I\'ll email you when it\'s paid.',
    'add_product_to_invoice' => 'Add 1 :product',
    'not_authorized' => 'You are not authorized',
    'email_not_found' => 'I wasn\'t able to find an available account for :email',
    'invalid_code' => 'The code is not correct',
    'list_products' => 'List Products',

    'include_item_taxes_inline' => 'Include <b>line item taxes in line total</b>',
    'created_quotes' => 'Successfully created :count quotes(s)',
    'warning' => 'Warning',
    'self-update' => 'Update',
    'update_system_title' => 'Update Ozoo ERP',
    'update_system_warning' => 'Before start upgrading Ozoo ERP create a backup of your database and files!',
    'update_system_available' => 'A new version of Ozoo ERP is available.',
    'update_system_unavailable' => 'No new version of Ozoo ERP available.',
    'update_invoiceninja_update_start' => 'Update now',
    'update_invoiceninja_download_start' => 'Download :version',
    'create_new' => 'Create New',

    'toggle_navigation' => 'Toggle Navigation',
    'toggle_history' => 'Toggle History',
    'unassigned' => 'Unassigned',
    'task' => 'Task',
    'contact_name' => 'Contact Name',
    'city_state_postal' => 'City/State/Postal',
    'postal_city' => 'Postal/City',
    'custom_field' => 'Custom Field',
    'account_fields' => 'Company Fields',
    'facebook_and_twitter' => 'Facebook and Twitter',
    'facebook_and_twitter_help' => 'Follow our feeds to help support our project',
    'reseller_text' => 'Note: the white-label license is intended for personal use, please email us at :email if you\'d like to resell the app.',
    'unnamed_client' => 'Unnamed Client',

    'day' => 'Day',
    'week' => 'Week',
    'month' => 'Month',
    'inactive_logout' => 'You have been logged out due to inactivity',
    'reports' => 'Reports',
    'total_profit' => 'Total Profit',
    'total_expenses' => 'Total Expenses',
    'quote_to' => 'Quote to',

    // Limits
    'limit' => 'Limit',
    'min_limit' => 'Min: :min',
    'max_limit' => 'Max: :max',
    'no_limit' => 'No Limits',
    'set_limits' => 'Set :gateway_type Limits',
    'enable_min' => 'Enable min',
    'enable_max' => 'Enable max',
    'min' => 'Min',
    'max' => 'Max',
    'limits_not_met' => 'This invoice does not meet the limits for that payment type.',

    'date_range' => 'Date Range',
    'raw' => 'Raw',
    'raw_html' => 'Raw HTML',
    'update' => 'Update',
    'invoice_fields_help' => 'Drag and drop fields to change their order and location',
    'new_category' => 'New Category',
    'restore_product' => 'Restore Product',
    'blank' => 'Blank',
    'invoice_save_error' => 'There was an error saving your invoice',
    'enable_recurring' => 'Enable Recurring',
    'disable_recurring' => 'Disable Recurring',
    'text' => 'Text',
    'expense_will_create' => 'expense will be created',
    'expenses_will_create' => 'expenses will be created',
    'created_expenses' => 'Successfully created :count expense(s)',

    'translate_app' => 'Help improve our translations with :link',
    'expense_category' => 'Expense Category',

    'go_ninja_pro' => 'Go Ninja Pro!',
    'go_enterprise' => 'Go Enterprise!',
    'upgrade_for_features' => 'Upgrade For More Features',
    'pay_annually_discount' => 'Pay annually for 10 months + 2 free!',
    'pro_upgrade_title' => 'Ninja Pro',
    'pro_upgrade_feature1' => 'YourBrand.InvoiceNinja.com',
    'pro_upgrade_feature2' => 'Customize every aspect of your invoice!',
    'enterprise_upgrade_feature1' => 'Set permissions for multiple-users',
    'enterprise_upgrade_feature2' => 'Attach 3rd party files to invoices & expenses',
    'much_more' => 'Much More!',
    'all_pro_fetaures' => 'Plus all pro features!',

    'currency_symbol' => 'Symbol',
    'currency_code' => 'Code',

    'buy_license' => 'Buy License',
    'apply_license' => 'Apply License',
    'submit' => 'Submit',
    'white_label_license_key' => 'License Key',
    'invalid_white_label_license' => 'The white label license is not valid',
    'created_by' => 'Created by :name',
    'modules' => 'Modules',
    'financial_year_start' => 'First Month of the Year',
    'authentication' => 'Authentication',
    'checkbox' => 'Checkbox',
    'invoice_signature' => 'Signature',
    'show_accept_invoice_terms' => 'Invoice Terms Checkbox',
    'show_accept_invoice_terms_help' => 'Require client to confirm that they accept the invoice terms.',
    'show_accept_quote_terms' => 'Quote Terms Checkbox',
    'show_accept_quote_terms_help' => 'Require client to confirm that they accept the quote terms.',
    'require_invoice_signature' => 'Invoice Signature',
    'require_invoice_signature_help' => 'Require client to provide their signature.',
    'require_quote_signature' => 'Quote Signature',
    'require_quote_signature_help' => 'Require client to provide their signature.',
    'i_agree' => 'I Agree To The Terms',
    'sign_here' => 'Please sign here:',
    'sign_here_ux_tip' => 'Use the mouse or your touchpad to trace your signature.',
    'authorization' => 'Authorization',
    'signed' => 'Signed',

    'vendor_name' => 'Vendor',
    'entity_state' => 'State',
    'client_created_at' => 'Date Created',
    'postmark_error' => 'There was a problem sending the email through Postmark: :link',
    'project' => 'Project',
    'projects' => 'Projects',
    'new_project' => 'New Project',
    'edit_project' => 'Edit Project',
    'archive_project' => 'Archive Project',
    'list_projects' => 'List Projects',
    'updated_project' => 'Successfully updated project',
    'created_project' => 'Successfully created project',
    'archived_project' => 'Successfully archived project',
    'archived_projects' => 'Successfully archived :count projects',
    'restore_project' => 'Restore Project',
    'restored_project' => 'Successfully restored project',
    'delete_project' => 'Delete Project',
    'deleted_project' => 'Successfully deleted project',
    'deleted_projects' => 'Successfully deleted :count projects',
    'delete_expense_category' => 'Delete category',
    'deleted_expense_category' => 'Successfully deleted category',
    'delete_product' => 'Delete Product',
    'deleted_product' => 'Successfully deleted product',
    'deleted_products' => 'Successfully deleted :count products',
    'restored_product' => 'Successfully restored product',
    'update_credit' => 'Update Credit',
    'updated_credit' => 'Successfully updated credit',
    'edit_credit' => 'Edit Credit',
    'realtime_preview' => 'Realtime Preview',
    'realtime_preview_help' => 'Realtime refresh PDF preview on the invoice page when editing invoice.<br/>Disable this to improve performance when editing invoices.',
    'live_preview_help' => 'Display a live PDF preview on the invoice page.',
    'force_pdfjs_help' => 'Replace the built-in PDF viewer in :chrome_link and :firefox_link.<br/>Enable this if your browser is automatically downloading the PDF.',
    'force_pdfjs' => 'Prevent Download',
    'redirect_url' => 'Redirect URL',
    'redirect_url_help' => 'Optionally specify a URL to redirect to after a payment is entered.',
    'save_draft' => 'Save Draft',
    'refunded_credit_payment' => 'Refunded credit payment',
    'keyboard_shortcuts' => 'Keyboard Shortcuts',
    'toggle_menu' => 'Toggle Menu',
    'new_...' => 'New ...',
    'list_...' => 'List ...',
    'created_at' => 'Date Created',
    'contact_us' => 'Contact Us',
    'user_guide' => 'User Guide',
    'promo_message' => 'Upgrade before :expires and get :amount OFF your first year of our Pro or Enterprise packages.',
    'discount_message' => ':amount off expires :expires',
    'mark_paid' => 'Mark Paid',
    'marked_sent_invoice' => 'Successfully marked invoice sent',
    'marked_sent_invoices' => 'Successfully marked invoices sent',
    'invoice_name' => 'Invoice',
    'product_will_create' => 'product will be created',
    'contact_us_response' => 'Thank you for your message! We\'ll try to respond as soon as possible.',
    'last_7_days' => 'Last 7 Days',
    'last_30_days' => 'Last 30 Days',
    'this_month' => 'This Month',
    'last_month' => 'Last Month',
    'current_quarter' => 'Current Quarter',
    'last_quarter' => 'Last Quarter',
    'last_year' => 'Last Year',
    'all_time' => 'All Time',
    'custom_range' => 'Custom Range',
    'url' => 'URL',
    'debug' => 'Debug',
    'https' => 'HTTPS',
    'require' => 'Require',
    'license_expiring' => 'Note: Your license will expire in :count days, :link to renew it.',
    'security_confirmation' => 'Your email address has been confirmed.',
    'white_label_expired' => 'Your white label license has expired, please consider renewing it to help support our project.',
    'renew_license' => 'Renew License',
    'iphone_app_message' => 'Consider downloading our :link',
    'iphone_app' => 'iPhone app',
    'android_app' => 'Android app',
    'logged_in' => 'Logged In',
    'switch_to_primary' => 'Switch to your primary company (:name) to manage your plan.',
    'inclusive' => 'Inclusive',
    'exclusive' => 'Exclusive',
    'postal_city_state' => 'Postal/City/State',
    'phantomjs_help' => 'In certain cases the app uses :link_phantom to generate the PDF, install :link_docs to generate it locally.',
    'phantomjs_local' => 'Using local PhantomJS',
    'client_number' => 'Client Number',
    'client_number_help' => 'Specify a prefix or use a custom pattern to dynamically set the client number.',
    'next_client_number' => 'The next client number is :number.',
    'generated_numbers' => 'Generated Numbers',
    'notes_reminder1' => 'First Reminder',
    'notes_reminder2' => 'Second Reminder',
    'notes_reminder3' => 'Third Reminder',
    'notes_reminder4' => 'Reminder',
    'bcc_email' => 'BCC Email',
    'tax_quote' => 'Tax Quote',
    'tax_invoice' => 'Tax Invoice',
    'emailed_invoices' => 'Successfully emailed invoices',
    'emailed_quotes' => 'Successfully emailed quotes',
    'website_url' => 'Website URL',
    'domain' => 'Domain',
    'domain_help' => 'Used in the client portal and when sending emails.',
    'domain_help_website' => 'Used when sending emails.',
    'import_invoices' => 'Import Invoices',
    'new_report' => 'New Report',
    'edit_report' => 'Edit Report',
    'columns' => 'Columns',
    'filters' => 'Filters',
    'sort_by' => 'Sort By',
    'draft' => 'Draft',
    'unpaid' => 'Unpaid',
    'aging' => 'Aging',
    'age' => 'Age',
    'days' => 'Days',
    'age_group_0' => '0 - 30 Days',
    'age_group_30' => '30 - 60 Days',
    'age_group_60' => '60 - 90 Days',
    'age_group_90' => '90 - 120 Days',
    'age_group_120' => '120+ Days',
    'invoice_details' => 'Invoice Details',
    'qty' => 'Quantity',
    'profit_and_loss' => 'Profit and Loss',
    'revenue' => 'Revenue',
    'profit' => 'Profit',
    'group_when_sorted' => 'Group Sort',
    'group_dates_by' => 'Group Dates By',
    'year' => 'Year',
    'view_statement' => 'View Statement',
    'statement' => 'Statement',
    'statement_date' => 'Statement Date',
    'mark_active' => 'Mark Active',
    'send_automatically' => 'Send Automatically',
    'initial_email' => 'Initial Email',
    'invoice_not_emailed' => 'This invoice hasn\'t been emailed.',
    'quote_not_emailed' => 'This quote hasn\'t been emailed.',
    'sent_by' => 'Sent by :user',
    'recipients' => 'Recipients',
    'save_as_default' => 'Save as default',
    'start_of_week_help' => 'Used by <b>date</b> selectors',
    'financial_year_start_help' => 'Used by <b>date range</b> selectors',
    'reports_help' => 'Shift + Click to sort by multiple columns, Ctrl + Click to clear the grouping.',
    'this_year' => 'This Year',

    // Updated login screen
    'ninja_tagline' => 'Create. Send. Get Paid.',
    'login_or_existing' => 'Or login with a connected account.',
    'sign_up_now' => 'Sign Up Now',
    'not_a_member_yet' => 'Not a member yet?',
    'login_create_an_account' => 'Create an Account!',

    // New Client Portal styling
    'invoice_from' => 'Invoices From:',
    'full_name' => 'Full Name',
    'month_year' => 'MONTH/YEAR',
    'valid_thru' => 'Valid\nthru',

    'product_fields' => 'Product Fields',
    'custom_product_fields_help' => 'Add a field when creating a product or invoice and display the label and value on the PDF.',
    'freq_two_months' => 'Two months',
    'freq_yearly' => 'Annually',
    'profile' => 'Profile',
    'industry_Construction' => 'Construction',
    'your_statement' => 'Your Statement',
    'statement_issued_to' => 'Statement issued to',
    'statement_to' => 'Statement to',
    'customize_options' => 'Customize options',
    'created_payment_term' => 'Successfully created payment term',
    'updated_payment_term' => 'Successfully updated payment term',
    'archived_payment_term' => 'Successfully archived payment term',
    'resend_invite' => 'Resend Invitation',
    'credit_created_by' => 'Credit created by payment :transaction_reference',
    'created_payment_and_credit' => 'Successfully created payment and credit',
    'created_payment_and_credit_emailed_client' => 'Successfully created payment and credit, and emailed client',
    'create_project' => 'Create project',
    'create_vendor' => 'Create vendor',
    'create_expense_category' => 'Create category',
    'pro_plan_reports' => ':link to enable reports by joining the Pro Plan',
    'mark_ready' => 'Mark Ready',
    'limits' => 'Limits',
    'fees' => 'Fees',
    'fee' => 'Fee',
    'set_limits_fees' => 'Set :gateway_type Limits/Fees',
    'fees_tax_help' => 'Enable line item taxes to set the fee tax rates.',
    'fees_sample' => 'The fee for a :amount invoice would be :total.',
    'discount_sample' => 'The discount for a :amount invoice would be :total.',
    'no_fees' => 'No Fees',
    'gateway_fees_disclaimer' => 'Warning: not all states/payment gateways allow adding fees, please review local laws/terms of service.',
    'percent' => 'Percent',
    'location' => 'Location',
    'line_item' => 'Line Item',
    'surcharge' => 'Surcharge',
    'location_first_surcharge' => 'Enabled - First surcharge',
    'location_second_surcharge' => 'Enabled - Second surcharge',
    'location_line_item' => 'Enabled - Line item',
    'online_payment_surcharge' => 'Online Payment Surcharge',
    'gateway_fees' => 'Gateway Fees',
    'fees_disabled' => 'Fees are disabled',
    'gateway_fees_help' => 'Automatically add an online payment surcharge/discount.',
    'gateway' => 'Gateway',
    'gateway_fee_change_warning' => 'If there are unpaid invoices with fees they need to be updated manually.',
    'fees_surcharge_help' => 'Customize surcharge :link.',
    'label_and_taxes' => 'label and taxes',
    'billable' => 'Billable',
    'logo_warning_too_large' => 'The image file is too large.',
    'logo_warning_fileinfo' => 'Warning: To support gifs the fileinfo PHP extension needs to be enabled.',
    'logo_warning_invalid' => 'There was a problem reading the image file, please try a different format.',
    'error_refresh_page' => 'An error occurred, please refresh the page and try again.',
    'data' => 'Data',
    'imported_settings' => 'Successfully imported settings',
    'reset_counter' => 'Reset Counter',
    'next_reset' => 'Next Reset',
    'reset_counter_help' => 'Automatically reset the invoice and quote counters.',
    'auto_bill_failed' => 'Auto-billing for invoice :invoice_number failed',
    'online_payment_discount' => 'Online Payment Discount',
    'created_new_company' => 'Successfully created new company',
    'fees_disabled_for_gateway' => 'Fees are disabled for this gateway.',
    'logout_and_delete' => 'Log Out/Delete Account',
    'tax_rate_type_help' => 'Inclusive tax rates adjust the line item cost when selected.<br/>Only exclusive tax rates can be used as a default.',
    'credit_note' => 'Credit Note',
    'credit_issued_to' => 'Credit issued to',
    'credit_to' => 'Credit to',
    'your_credit' => 'Your Credit',
    'credit_number' => 'Credit Number',
    'create_credit_note' => 'Create Credit Note',
    'menu' => 'Menu',
    'error_incorrect_gateway_ids' => 'Error: The gateways table has incorrect ids.',
    'purge_data' => 'Purge Data',
    'delete_data' => 'Delete Data',
    'purge_data_help' => 'Permanently delete all data but keep the account and settings.',
    'cancel_account_help' => 'Permanently delete the account along with all data and setting.',
    'purge_successful' => 'Successfully purged company data',
    'forbidden' => 'Forbidden',
    'purge_data_message' => 'Warning: This will permanently erase your data, there is no undo.',
    'contact_phone' => 'Contact Phone',
    'contact_email' => 'Contact Email',
    'reply_to_email' => 'Reply-To Email',
    'reply_to_email_help' => 'Specify the reply-to address for client emails.',
    'bcc_email_help' => 'Privately include this address with client emails.',
    'import_complete' => 'Your import has successfully completed.',
    'confirm_account_to_import' => 'Please confirm your account to import data.',
    'import_started' => 'Your import has started, we\'ll send you an email once it completes.',
    'payment_type_Venmo' => 'Venmo',
    'payment_type_Money Order' => 'Money Order',
    'archived_products' => 'Successfully archived :count products',
    'recommend_on' => 'We recommend <b>enabling</b> this setting.',
    'recommend_off' => 'We recommend <b>disabling</b> this setting.',
    'notes_auto_billed' => 'Auto-billed',
    'surcharge_label' => 'Surcharge Label',
    'contact_fields' => 'Contact Fields',
    'custom_contact_fields_help' => 'Add a field when creating a contact and optionally display the label and value on the PDF.',
    'datatable_info' => 'Showing :start to :end of :total entries',
    'credit_total' => 'Credit Total',
    'mark_billable' => 'Mark billable',
    'billed' => 'Billed',
    'company_variables' => 'Company Variables',
    'client_variables' => 'Client Variables',
    'invoice_variables' => 'Invoice Variables',
    'navigation_variables' => 'Navigation Variables',
    'custom_variables' => 'Custom Variables',
    'invalid_file' => 'Invalid file type',
    'add_documents_to_invoice' => 'Add Documents to Invoice',
    'mark_expense_paid' => 'Mark paid',
    'white_label_license_error' => 'Failed to validate the license, either expired or excessive activations. Email <EMAIL> for more information.',
    'plan_price' => 'Plan Price',
    'wrong_confirmation' => 'Incorrect confirmation code',
    'oauth_taken' => 'The account is already registered',
    'emailed_payment' => 'Successfully emailed payment',
    'email_payment' => 'Email Payment',
    'invoiceplane_import' => 'Use :link to migrate your data from InvoicePlane.',
    'duplicate_expense_warning' => 'Warning: This :link may be a duplicate',
    'expense_link' => 'expense',
    'resume_task' => 'Resume Task',
    'resumed_task' => 'Successfully resumed task',
    'quote_design' => 'Quote Design',
    'default_design' => 'Standard Design',
    'custom_design1' => 'Custom Design 1',
    'custom_design2' => 'Custom Design 2',
    'custom_design3' => 'Custom Design 3',
    'empty' => 'Empty',
    'load_design' => 'Load Design',
    'accepted_card_logos' => 'Accepted Card Logos',
    'google_analytics' => 'Google Analytics',
    'analytics_key' => 'Analytics Key',
    'analytics_key_help' => 'Track payments using :link',
    'start_date_required' => 'The start date is required',
    'application_settings' => 'Application Settings',
    'database_connection' => 'Database Connection',
    'driver' => 'Driver',
    'host' => 'Host',
    'database' => 'Database',
    'test_connection' => 'Test connection',
    'from_name' => 'From Name',
    'from_address' => 'From Address',
    'port' => 'Port',
    'encryption' => 'Encryption',
    'mailgun_domain' => 'Mailgun Domain',
    'mailgun_private_key' => 'Mailgun Private Key',
    'brevo_domain' => 'Brevo Domain',
    'brevo_private_key' => 'Brevo Private Key',
    'send_test_email' => 'Send Test Email',
    'select_label' => 'Select Label',
    'label' => 'Label',
    'service' => 'Service',
    'update_payment_details' => 'Update payment details',
    'updated_payment_details' => 'Successfully updated payment details',
    'update_credit_card' => 'Update Credit Card',
    'recurring_expenses' => 'Recurring Expenses',
    'recurring_expense' => 'Recurring Expense',
    'new_recurring_expense' => 'New Recurring Expense',
    'edit_recurring_expense' => 'Edit Recurring Expense',
    'archive_recurring_expense' => 'Archive Recurring Expense',
    'list_recurring_expense' => 'List Recurring Expenses',
    'updated_recurring_expense' => 'Successfully updated recurring expense',
    'created_recurring_expense' => 'Successfully created recurring expense',
    'archived_recurring_expense' => 'Successfully archived recurring expense',
    'restore_recurring_expense' => 'Restore Recurring Expense',
    'restored_recurring_expense' => 'Successfully restored recurring expense',
    'delete_recurring_expense' => 'Delete Recurring Expense',
    'deleted_recurring_expense' => 'Successfully deleted recurring expense',
    'view_recurring_expense' => 'View Recurring Expense',
    'taxes_and_fees' => 'Taxes and fees',
    'import_failed' => 'Import Failed',
    'recurring_prefix' => 'Recurring Prefix',
    'options' => 'Options',
    'credit_number_help' => 'Specify a prefix or use a custom pattern to dynamically set the credit number for negative invoices.',
    'next_credit_number' => 'The next credit number is :number.',
    'padding_help' => 'The number of zero\'s to pad the number.',
    'import_warning_invalid_date' => 'Warning: The date format appears to be invalid.',
    'product_notes' => 'Product Notes',
    'app_version' => 'App Version',
    'ofx_version' => 'OFX Version',
    'charge_late_fee' => 'Charge Late Fee',
    'late_fee_amount' => 'Late Fee Amount',
    'late_fee_percent' => 'Late Fee Percent',
    'late_fee_added' => 'Late fee added on :date',
    'download_invoice' => 'Download Invoice',
    'download_quote' => 'Download Quote',
    'invoices_are_attached' => 'Your invoice PDFs are attached.',
    'downloaded_invoice' => 'An email will be sent with the invoice PDF',
    'downloaded_quote' => 'An email will be sent with the quote PDF',
    'downloaded_invoices' => 'An email will be sent with the invoice PDFs',
    'downloaded_quotes' => 'An email will be sent with the quote PDFs',
    'clone_expense' => 'Clone Expense',
    'default_documents' => 'Default Documents',
    'send_email_to_client' => 'Send email to the client',
    'refund_subject' => 'Refund Processed',
    'refund_body' => 'You have been processed a refund of :amount for invoice :invoice_number.',

    'currency_us_dollar' => 'US Dollar',
    'currency_british_pound' => 'British Pound',
    'currency_euro' => 'Euro',
    'currency_south_african_rand' => 'South African Rand',
    'currency_danish_krone' => 'Danish Krone',
    'currency_israeli_shekel' => 'Israeli Shekel',
    'currency_swedish_krona' => 'Swedish Krona',
    'currency_kenyan_shilling' => 'Kenyan Shilling',
    'currency_canadian_dollar' => 'Canadian Dollar',
    'currency_philippine_peso' => 'Philippine Peso',
    'currency_indian_rupee' => 'Indian Rupee',
    'currency_australian_dollar' => 'Australian Dollar',
    'currency_singapore_dollar' => 'Singapore Dollar',
    'currency_norske_kroner' => 'Norske Kroner',
    'currency_new_zealand_dollar' => 'New Zealand Dollar',
    'currency_vietnamese_dong' => 'Vietnamese Dong',
    'currency_swiss_franc' => 'Swiss Franc',
    'currency_guatemalan_quetzal' => 'Guatemalan Quetzal',
    'currency_malaysian_ringgit' => 'Malaysian Ringgit',
    'currency_brazilian_real' => 'Brazilian Real',
    'currency_thai_baht' => 'Thai Baht',
    'currency_nigerian_naira' => 'Nigerian Naira',
    'currency_argentine_peso' => 'Argentine Peso',
    'currency_bangladeshi_taka' => 'Bangladeshi Taka',
    'currency_united_arab_emirates_dirham' => 'United Arab Emirates Dirham',
    'currency_hong_kong_dollar' => 'Hong Kong Dollar',
    'currency_indonesian_rupiah' => 'Indonesian Rupiah',
    'currency_mexican_peso' => 'Mexican Peso',
    'currency_egyptian_pound' => 'Egyptian Pound',
    'currency_colombian_peso' => 'Colombian Peso',
    'currency_west_african_franc' => 'West African Franc',
    'currency_chinese_renminbi' => 'Chinese Renminbi',
    'currency_rwandan_franc' => 'Rwandan Franc',
    'currency_tanzanian_shilling' => 'Tanzanian Shilling',
    'currency_netherlands_antillean_guilder' => 'Netherlands Antillean Guilder',
    'currency_trinidad_and_tobago_dollar' => 'Trinidad and Tobago Dollar',
    'currency_east_caribbean_dollar' => 'East Caribbean Dollar',
    'currency_ghanaian_cedi' => 'Ghanaian Cedi',
    'currency_bulgarian_lev' => 'Bulgarian Lev',
    'currency_aruban_florin' => 'Aruban Florin',
    'currency_turkish_lira' => 'Turkish Lira',
    'currency_romanian_new_leu' => 'Romanian New Leu',
    'currency_croatian_kuna' => 'Croatian Kuna',
    'currency_saudi_riyal' => 'Saudi Riyal',
    'currency_japanese_yen' => 'Japanese Yen',
    'currency_maldivian_rufiyaa' => 'Maldivian Rufiyaa',
    'currency_costa_rican_colon' => 'Costa Rican Colón',
    'currency_pakistani_rupee' => 'Pakistani Rupee',
    'currency_polish_zloty' => 'Polish Zloty',
    'currency_sri_lankan_rupee' => 'Sri Lankan Rupee',
    'currency_czech_koruna' => 'Czech Koruna',
    'currency_uruguayan_peso' => 'Uruguayan Peso',
    'currency_namibian_dollar' => 'Namibian Dollar',
    'currency_tunisian_dinar' => 'Tunisian Dinar',
    'currency_russian_ruble' => 'Russian Ruble',
    'currency_mozambican_metical' => 'Mozambican Metical',
    'currency_omani_rial' => 'Omani Rial',
    'currency_ukrainian_hryvnia' => 'Ukrainian Hryvnia',
    'currency_macanese_pataca' => 'Macanese Pataca',
    'currency_taiwan_new_dollar' => 'Taiwan New Dollar',
    'currency_dominican_peso' => 'Dominican Peso',
    'currency_chilean_peso' => 'Chilean Peso',
    'currency_icelandic_krona' => 'Icelandic Króna',
    'currency_papua_new_guinean_kina' => 'Papua New Guinean Kina',
    'currency_jordanian_dinar' => 'Jordanian Dinar',
    'currency_myanmar_kyat' => 'Myanmar Kyat',
    'currency_peruvian_sol' => 'Peruvian Sol',
    'currency_botswana_pula' => 'Botswana Pula',
    'currency_hungarian_forint' => 'Hungarian Forint',
    'currency_ugandan_shilling' => 'Ugandan Shilling',
    'currency_barbadian_dollar' => 'Barbadian Dollar',
    'currency_brunei_dollar' => 'Brunei Dollar',
    'currency_georgian_lari' => 'Georgian Lari',
    'currency_qatari_riyal' => 'Qatari Riyal',
    'currency_honduran_lempira' => 'Honduran Lempira',
    'currency_surinamese_dollar' => 'Surinamese Dollar',
    'currency_bahraini_dinar' => 'Bahraini Dinar',
    'currency_venezuelan_bolivars' => 'Venezuelan Bolivars',
    'currency_south_korean_won' => 'South Korean Won',
    'currency_moroccan_dirham' => 'Moroccan Dirham',
    'currency_jamaican_dollar' => 'Jamaican Dollar',
    'currency_angolan_kwanza' => 'Angolan Kwanza',
    'currency_haitian_gourde' => 'Haitian Gourde',
    'currency_zambian_kwacha' => 'Zambian Kwacha',
    'currency_nepalese_rupee' => 'Nepalese Rupee',
    'currency_cfp_franc' => 'CFP Franc',
    'currency_mauritian_rupee' => 'Mauritian Rupee',
    'currency_cape_verdean_escudo' => 'Cape Verdean Escudo',
    'currency_kuwaiti_dinar' => 'Kuwaiti Dinar',
    'currency_algerian_dinar' => 'Algerian Dinar',
    'currency_macedonian_denar' => 'Macedonian Denar',
    'currency_fijian_dollar' => 'Fijian Dollar',
    'currency_bolivian_boliviano' => 'Bolivian Boliviano',
    'currency_albanian_lek' => 'Albanian Lek',
    'currency_serbian_dinar' => 'Serbian Dinar',
    'currency_lebanese_pound' => 'Lebanese Pound',
    'currency_armenian_dram' => 'Armenian Dram',
    'currency_azerbaijan_manat' => 'Azerbaijan Manat',
    'currency_bosnia_and_herzegovina_convertible_mark' => 'Bosnia and Herzegovina Convertible Mark',
    'currency_belarusian_ruble' => 'Belarusian Ruble',
    'currency_moldovan_leu' => 'Moldovan Leu',
    'currency_kazakhstani_tenge' => 'Kazakhstani Tenge',
    'currency_gibraltar_pound' => 'Gibraltar Pound',

    'currency_gambia_dalasi' => 'Gambia Dalasi',
    'currency_paraguayan_guarani' => 'Paraguayan Guarani',
    'currency_malawi_kwacha' => 'Malawi Kwacha',
    'currency_zimbabwean_dollar' => 'Zimbabwean Dollar',
    'currency_cambodian_riel' => 'Cambodian Riel',
    'currency_vanuatu_vatu' => 'Vanuatu Vatu',

    'currency_cuban_peso' => 'Cuban Peso',
    'currency_bz_dollar' => 'BZ Dollar',
    'currency_libyan_dinar' => 'Libyan Dinar',
    'currency_silver_troy_ounce' => 'Silver Troy Ounce',
    'currency_gold_troy_ounce' => 'Gold Troy Ounce',
    'currency_nicaraguan_córdoba' => 'Nicaraguan Córdoba',
    'currency_malagasy_ariary' => 'Malagasy ariary',
    "currency_tongan_pa_anga" => "Tongan Pa'anga",

    'review_app_help' => 'We hope you\'re enjoying using the app.<br/>If you\'d consider :link we\'d greatly appreciate it!',
    'writing_a_review' => 'writing a review',

    'tax1' => 'First Tax',
    'tax2' => 'Second Tax',
    'fee_help' => 'Gateway fees are the costs charged for access to the financial networks that handle the processing of online payments.',
    'format_export' => 'Exporting format',
    'custom1' => 'First Custom',
    'custom2' => 'Second Custom',
    'contact_first_name' => 'Contact First Name',
    'contact_last_name' => 'Contact Last Name',
    'contact_custom1' => 'Contact First Custom',
    'contact_custom2' => 'Contact Second Custom',
    'currency' => 'Currency',
    'ofx_help' => 'To troubleshoot check for comments on :ofxhome_link and test with :ofxget_link.',
    'comments' => 'comments',

    'item_product' => 'Item Product',
    'item_notes' => 'Item Notes',
    'item_cost' => 'Item Cost',
    'item_quantity' => 'Item Quantity',
    'item_tax_rate' => 'Item Tax Rate',
    'item_tax_name' => 'Item Tax Name',
    'item_tax1' => 'Item Tax1',
    'item_tax2' => 'Item Tax2',

    'delete_company' => 'Delete Company',
    'delete_company_help' => 'Permanently delete the company along with all data and setting.',
    'delete_company_message' => 'Warning: This will permanently delete your company, there is no undo.',

    'applied_discount' => 'The coupon has been applied, the plan price has been reduced by :discount%.',
    'applied_free_year' => 'The coupon has been applied, your account has been upgraded to pro for one year.',

    'contact_us_help' => 'If you\'re reporting an error please include any relevant logs from storage/logs/laravel-error.log',
    'include_errors' => 'Include Errors',
    'include_errors_help' => 'Include :link from storage/logs/laravel-error.log',
    'recent_errors' => 'recent errors',
    'customer' => 'Customer',
    'customers' => 'Customers',
    'created_customer' => 'Successfully created customer',
    'created_customers' => 'Successfully created :count customers',

    'purge_details' => 'The data in your company (:account) has been successfully purged.',
    'deleted_company' => 'Successfully deleted company',
    'deleted_account' => 'Successfully canceled account',
    'deleted_company_details' => 'Your company (:account) has been successfully deleted.',
    'deleted_account_details' => 'Your account (:account) has been successfully deleted.',

    'alipay' => 'Alipay',
    'sofort' => 'Sofort',
    'sepa' => 'SEPA Direct Debit',
    'name_without_special_characters' => 'Please enter a name with only the letters a-z and whitespaces',
    'enable_alipay' => 'Accept Alipay',
    'enable_sofort' => 'Accept EU bank transfers',
    'stripe_alipay_help' => 'These gateways also need to be activated in :link.',
    'calendar' => 'Calendar',
    'pro_plan_calendar' => ':link to enable the calendar by joining the Pro Plan',

    'what_are_you_working_on' => 'What are you working on?',
    'time_tracker' => 'Time Tracker',
    'refresh' => 'Refresh',
    'filter_sort' => 'Filter/Sort',
    'no_description' => 'No Description',
    'time_tracker_login' => 'Time Tracker Login',
    'save_or_discard' => 'Save or discard your changes',
    'discard_changes' => 'Discard Changes',
    'tasks_not_enabled' => 'Tasks are not enabled.',
    'started_task' => 'Successfully started task',
    'create_client' => 'Create Client',

    'download_desktop_app' => 'Download the desktop app',
    'download_iphone_app' => 'Download the iPhone app',
    'download_android_app' => 'Download the Android app',
    'time_tracker_mobile_help' => 'Double tap a task to select it',
    'stopped' => 'Stopped',
    'ascending' => 'Ascending',
    'descending' => 'Descending',
    'sort_field' => 'Sort By',
    'sort_direction' => 'Direction',
    'discard' => 'Discard',
    'time_am' => 'AM',
    'time_pm' => 'PM',
    'time_mins' => 'mins',
    'time_hr' => 'hr',
    'time_hrs' => 'hrs',
    'clear' => 'Clear',
    'warn_payment_gateway' => 'Note: accepting online payments requires a payment gateway, :link to add one.',
    'task_rate' => 'Task Rate',
    'task_rate_help' => 'Set the default rate for invoiced tasks.',
    'past_due' => 'Past Due',
    'document' => 'Document',
    'invoice_or_expense' => 'Invoice/Expense',
    'invoice_pdfs' => 'Invoice PDFs',
    'enable_sepa' => 'Accept SEPA',
    'enable_bitcoin' => 'Accept Bitcoin',
    'iban' => 'IBAN',
    'sepa_authorization' => 'By providing your IBAN and confirming this payment, you are authorizing :company and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited.',
    'recover_license' => 'Recover License',
    'purchase' => 'Purchase',
    'recover' => 'Recover',
    'apply' => 'Apply',
    'recover_white_label_header' => 'Recover White Label License',
    'apply_white_label_header' => 'Apply White Label License',
    'videos' => 'Videos',
    'video' => 'Video',
    'return_to_invoice' => 'Return to Invoice',
    'partial_due_date' => 'Partial Due Date',
    'task_fields' => 'Task Fields',
    'product_fields_help' => 'Drag and drop fields to change their order',
    'custom_value1' => 'Custom Value 1',
    'custom_value2' => 'Custom Value 2',
    'enable_two_factor' => 'Two-Factor Authentication',
    'enable_two_factor_help' => 'Use your phone to confirm your identity when logging in',
    'two_factor_setup' => 'Two-Factor Setup',
    'two_factor_setup_help' => 'Scan the bar code with a :link compatible app.',
    'one_time_password' => 'One Time Password',
    'set_phone_for_two_factor' => 'Set your mobile phone number as a backup to enable.',
    'enabled_two_factor' => 'Successfully enabled Two-Factor Authentication',
    'add_product' => 'Add Product',
    'email_will_be_sent_on' => 'Note: the email will be sent on :date.',
    'invoice_product' => 'Invoice Product',
    'self_host_login' => 'Self-Host Login',
    'set_self_hoat_url' => 'Self-Host URL',
    'local_storage_required' => 'Error: local storage is not available.',
    'your_password_reset_link' => 'Your Password Reset Link',
    'subdomain_taken' => 'The subdomain is already in use',
    'expense_mailbox_taken' => 'The inbound mailbox is already in use',
    'expense_mailbox_invalid' => 'The inbound mailbox does not match the required schema',
    'client_login' => 'Client Login',
    'converted_amount' => 'Converted Amount',
    'default' => 'Default',
    'shipping_address' => 'Shipping Address',
    'bllling_address' => 'Billing Address',
    'billing_address1' => 'Billing Street',
    'billing_address2' => 'Billing Apt/Suite',
    'billing_city' => 'Billing City',
    'billing_state' => 'Billing State/Province',
    'billing_postal_code' => 'Billing Postal Code',
    'billing_country' => 'Billing Country',
    'shipping_address1' => 'Shipping Street',
    'shipping_address2' => 'Shipping Apt/Suite',
    'shipping_city' => 'Shipping City',
    'shipping_state' => 'Shipping State/Province',
    'shipping_postal_code' => 'Shipping Postal Code',
    'shipping_country' => 'Shipping Country',
    'classify' => 'Classify',
    'show_shipping_address_help' => 'Require client to provide their shipping address',
    'ship_to_billing_address' => 'Ship to billing address',
    'delivery_note' => 'Delivery Note',
    'show_tasks_in_portal' => 'Show tasks in the client portal',
    'cancel_schedule' => 'Cancel Schedule',
    'scheduled_report' => 'Scheduled Report',
    'scheduled_report_help' => 'Email the :report report as :format to :email',
    'created_scheduled_report' => 'Successfully scheduled report',
    'deleted_scheduled_report' => 'Successfully canceled scheduled report',
    'scheduled_report_attached' => 'Your scheduled :type report is attached.',
    'scheduled_report_error' => 'Failed to create schedule report',
    'invalid_one_time_password' => 'Invalid one time password',
    'apple_pay' => 'Apple/Google Pay',
    'enable_apple_pay' => 'Accept Apple Pay and Pay with Google',
    'requires_subdomain' => 'This payment type requires that a :link.',
    'subdomain_is_set' => 'subdomain is set',
    'verification_file' => 'Verification File',
    'verification_file_missing' => 'The verification file is needed to accept payments.',
    'apple_pay_domain' => 'Use <code>:domain</code> as the domain in :link.',
    'apple_pay_not_supported' => 'Sorry, Apple/Google Pay isn\'t supported by your browser',
    'optional_payment_methods' => 'Optional Payment Methods',
    'add_subscription' => 'Add Subscription',
    'target_url' => 'Target',
    'target_url_help' => 'When the selected event occurs the app will post the entity to the target URL.',
    'event' => 'Event',
    'subscription_event_1' => 'Created Client',
    'subscription_event_2' => 'Created Invoice',
    'subscription_event_3' => 'Created Quote',
    'subscription_event_4' => 'Created Payment',
    'subscription_event_5' => 'Created Vendor',
    'subscription_event_6' => 'Updated Quote',
    'subscription_event_7' => 'Deleted Quote',
    'subscription_event_8' => 'Updated Invoice',
    'subscription_event_9' => 'Deleted Invoice',
    'subscription_event_10' => 'Updated Client',
    'subscription_event_11' => 'Deleted Client',
    'subscription_event_12' => 'Deleted Payment',
    'subscription_event_13' => 'Updated Vendor',
    'subscription_event_14' => 'Deleted Vendor',
    'subscription_event_15' => 'Created Expense',
    'subscription_event_16' => 'Updated Expense',
    'subscription_event_17' => 'Deleted Expense',
    'subscription_event_18' => 'Created Task',
    'subscription_event_19' => 'Updated Task',
    'subscription_event_20' => 'Deleted Task',
    'subscription_event_21' => 'Approved Quote',
    'subscriptions' => 'Subscriptions',
    'updated_subscription' => 'Successfully updated subscription',
    'created_subscription' => 'Successfully created subscription',
    'edit_subscription' => 'Edit Subscription',
    'archive_subscription' => 'Archive Subscription',
    'archived_subscription' => 'Successfully archived subscription',
    'project_error_multiple_clients' => 'The projects can\'t belong to different clients',
    'invoice_project' => 'Invoice Project',
    'module_recurring_invoice' => 'Recurring Invoices',
    'module_credit' => 'Credits',
    'module_quote' => 'Quotes & Proposals',
    'module_task' => 'Tasks & Projects',
    'module_expense' => 'Expenses & Vendors',
    'module_ticket' => 'Tickets',
    'reminders' => 'Reminders',
    'send_client_reminders' => 'Send email reminders',
    'can_view_tasks' => 'Tasks are visible in the portal',
    'is_not_sent_reminders' => 'Reminders are not sent',
    'promotion_footer' => 'Your promotion will expire soon, :link to upgrade now.',
    'unable_to_delete_primary' => 'Note: to delete this company first delete all linked companies.',
    'please_register' => 'Please register your account',
    'processing_request' => 'Processing request',
    'mcrypt_warning' => 'Warning: Mcrypt is deprecated, run :command to update your cipher.',
    'edit_times' => 'Edit Times',
    'inclusive_taxes_help' => 'Include <b>taxes in the cost</b>',
    'inclusive_taxes_notice' => 'This setting can not be changed once an invoice has been created.',
    'inclusive_taxes_warning' => 'Warning: existing invoices will need to be resaved',
    'copy_shipping' => 'Copy Shipping',
    'copy_billing' => 'Copy Billing',
    'quote_has_expired' => 'The quote has expired, please contact the merchant.',
    'empty_table_footer' => 'Showing 0 to 0 of 0 entries',
    'do_not_trust' => 'Do not remember this device',
    'trust_for_30_days' => 'Trust for 30 days',
    'trust_forever' => 'Trust forever',
    'kanban' => 'Kanban',
    'backlog' => 'Backlog',
    'ready_to_do' => 'Ready to do',
    'in_progress' => 'In progress',
    'add_status' => 'Add status',
    'archive_status' => 'Archive Status',
    'new_status' => 'New Status',
    'convert_products' => 'Convert Products',
    'convert_products_help' => 'Automatically convert product prices to the client\'s currency',
    'improve_client_portal_link' => 'Set a subdomain to shorten the client portal link.',
    'budgeted_hours' => 'Budgeted Hours',
    'progress' => 'Progress',
    'view_project' => 'View Project',
    'summary' => 'Summary',
    'endless_reminder' => 'Endless Reminder',
    'signature_on_invoice_help' => 'Add the following code to show your client\'s signature on the PDF.',
    'signature_on_pdf' => 'Show on PDF',
    'signature_on_pdf_help' => 'Show the client signature on the invoice/quote PDF.',
    'expired_white_label' => 'The white label license has expired',
    'return_to_login' => 'Return to Login',
    'convert_products_tip' => 'Note: add a :link named ":name" to see the exchange rate.',
    'amount_greater_than_balance' => 'The amount is greater than the invoice balance, a credit will be created with the remaining amount.',
    'custom_fields_tip' => 'Use <code>Label|Option1,Option2</code> to show a select box.',
    'client_information' => 'Client Information',
    'updated_client_details' => 'Successfully updated client details',
    'auto' => 'Auto',
    'tax_amount' => 'Tax Amount',
    'tax_paid' => 'Tax Paid',
    'none' => 'None',
    'proposal_message_button' => 'To view your proposal for :amount, click the button below.',
    'proposal' => 'Proposal',
    'proposals' => 'Proposals',
    'list_proposals' => 'List Proposals',
    'new_proposal' => 'New Proposal',
    'edit_proposal' => 'Edit Proposal',
    'archive_proposal' => 'Archive Proposal',
    'delete_proposal' => 'Delete Proposal',
    'created_proposal' => 'Successfully created proposal',
    'updated_proposal' => 'Successfully updated proposal',
    'archived_proposal' => 'Successfully archived proposal',
    'deleted_proposal' => 'Successfully archived proposal',
    'archived_proposals' => 'Successfully archived :count proposals',
    'deleted_proposals' => 'Successfully archived :count proposals',
    'restored_proposal' => 'Successfully restored proposal',
    'restore_proposal' => 'Restore Proposal',
    'snippet' => 'Snippet',
    'snippets' => 'Snippets',
    'proposal_snippet' => 'Snippet',
    'proposal_snippets' => 'Snippets',
    'new_proposal_snippet' => 'New Snippet',
    'edit_proposal_snippet' => 'Edit Snippet',
    'archive_proposal_snippet' => 'Archive Snippet',
    'delete_proposal_snippet' => 'Delete Snippet',
    'created_proposal_snippet' => 'Successfully created snippet',
    'updated_proposal_snippet' => 'Successfully updated snippet',
    'archived_proposal_snippet' => 'Successfully archived snippet',
    'deleted_proposal_snippet' => 'Successfully archived snippet',
    'archived_proposal_snippets' => 'Successfully archived :count snippets',
    'deleted_proposal_snippets' => 'Successfully archived :count snippets',
    'restored_proposal_snippet' => 'Successfully restored snippet',
    'restore_proposal_snippet' => 'Restore Snippet',
    'template' => 'Template',
    'templates' => 'Templates',
    'proposal_template' => 'Template',
    'proposal_templates' => 'Templates',
    'new_proposal_template' => 'New Template',
    'edit_proposal_template' => 'Edit Template',
    'archive_proposal_template' => 'Archive Template',
    'delete_proposal_template' => 'Delete Template',
    'created_proposal_template' => 'Successfully created template',
    'updated_proposal_template' => 'Successfully updated template',
    'archived_proposal_template' => 'Successfully archived template',
    'deleted_proposal_template' => 'Successfully archived template',
    'archived_proposal_templates' => 'Successfully archived :count templates',
    'deleted_proposal_templates' => 'Successfully archived :count templates',
    'restored_proposal_template' => 'Successfully restored template',
    'restore_proposal_template' => 'Restore Template',
    'proposal_category' => 'Category',
    'proposal_categories' => 'Categories',
    'new_proposal_category' => 'New Category',
    'edit_proposal_category' => 'Edit Category',
    'archive_proposal_category' => 'Archive Category',
    'delete_proposal_category' => 'Delete Category',
    'created_proposal_category' => 'Successfully created category',
    'updated_proposal_category' => 'Successfully updated category',
    'archived_proposal_category' => 'Successfully archived category',
    'deleted_proposal_category' => 'Successfully archived category',
    'archived_proposal_categories' => 'Successfully archived :count categories',
    'deleted_proposal_categories' => 'Successfully archived :count categories',
    'restored_proposal_category' => 'Successfully restored category',
    'restore_proposal_category' => 'Restore Category',
    'delete_status' => 'Delete Status',
    'standard' => 'Standard',
    'icon' => 'Icon',
    'proposal_not_found' => 'The requested proposal is not available',
    'create_proposal_category' => 'Create category',
    'clone_proposal_template' => 'Clone Template',
    'proposal_email' => 'Proposal Email',
    'proposal_subject' => 'New proposal :number from :account',
    'proposal_message' => 'To view your proposal for :amount, click the link below.',
    'emailed_proposal' => 'Successfully emailed proposal',
    'load_template' => 'Load Template',
    'no_assets' => 'No images, drag to upload',
    'add_image' => 'Add Image',
    'select_image' => 'Select Image',
    'upgrade_to_upload_images' => 'Upgrade to the Enterprise Plan to upload files & images',
    'delete_image' => 'Delete Image',
    'delete_image_help' => 'Warning: deleting the image will remove it from all proposals.',
    'amount_variable_help' => 'Note: the invoice $amount field will use the partial/deposit field if set otherwise it will use the invoice balance.',
    'taxes_are_included_help' => 'Note: Inclusive taxes have been enabled.',
    'taxes_are_not_included_help' => 'Note: Inclusive taxes are not enabled.',
    'change_requires_purge' => 'Changing this setting requires :link the account data.',
    'purging' => 'purging',
    'warning_local_refund' => 'The refund will be recorded in the app but will NOT be processed by the payment gateway.',
    'email_address_changed' => 'Email address has been changed',
    'email_address_changed_message' => 'The email address for your account has been changed from :old_email to :new_email.',
    'test' => 'Test',
    'beta' => 'Beta',
    'email_history' => 'Email History',
    'loading' => 'Loading',
    'no_messages_found' => 'No messages found',
    'processing' => 'Processing',
    'reactivate' => 'Reactivate',
    'reactivated_email' => 'The email address has been reactivated',
    'emails' => 'Emails',
    'opened' => 'Opened',
    'bounced' => 'Bounced',
    'total_sent' => 'Total Sent',
    'total_opened' => 'Total Opened',
    'total_bounced' => 'Total Bounced',
    'total_spam' => 'Total Spam',
    'platforms' => 'Platforms',
    'email_clients' => 'Email Clients',
    'mobile' => 'Mobile',
    'desktop' => 'Desktop',
    'webmail' => 'Webmail',
    'group' => 'Group',
    'subgroup' => 'Subgroup',
    'unset' => 'Unset',
    'received_new_payment' => 'You\'ve received a new payment!',
    'slack_webhook_help' => 'Receive payment notifications using :link.',
    'slack_incoming_webhooks' => 'Slack incoming webhooks',
    'accept' => 'Accept',
    'accepted_terms' => 'Successfully accepted the latest terms of service',
    'invalid_url' => 'Invalid URL',
    'workflow_settings' => 'Workflow Settings',
    'auto_email_invoice' => 'Auto Email',
    'auto_email_invoice_help' => 'Automatically email recurring invoices when created.',
    'auto_archive_invoice' => 'Auto Archive',
    'auto_archive_invoice_help' => 'Automatically archive invoices when paid.',
    'auto_archive_quote' => 'Auto Archive',
    'auto_archive_quote_help' => 'Automatically archive quotes when converted to invoice.',
    'require_approve_quote' => 'Require approve quote',
    'require_approve_quote_help' => 'Require clients to approve quotes.',
    'allow_approve_expired_quote' => 'Allow approve expired quote',
    'allow_approve_expired_quote_help' => 'Allow clients to approve expired quotes.',
    'invoice_workflow' => 'Invoice Workflow',
    'quote_workflow' => 'Quote Workflow',
    'client_must_be_active' => 'Error: the client must be active',
    'purge_client' => 'Purge Client',
    'purged_client' => 'Successfully purged client',
    'purge_client_warning' => 'All related records (invoices, tasks, expenses, documents, etc) will also be deleted.',
    'clone_product' => 'Clone Product',
    'item_details' => 'Item Details',
    'send_item_details_help' => 'Send line item details to the payment gateway.',
    'view_proposal' => 'View Proposal',
    'view_in_portal' => 'View in Portal',
    'cookie_message' => 'This website uses cookies to ensure you get the best experience on our website.',
    'got_it' => 'Got it!',
    'vendor_will_create' => 'vendor will be created',
    'vendors_will_create' => 'vendors will be created',
    'created_vendors' => 'Successfully created :count vendor(s)',
    'import_vendors' => 'Import Vendors',
    'company' => 'Company',
    'client_field' => 'Client Field',
    'contact_field' => 'Contact Field',
    'product_field' => 'Product Field',
    'task_field' => 'Task Field',
    'project_field' => 'Project Field',
    'expense_field' => 'Expense Field',
    'vendor_field' => 'Vendor Field',
    'company_field' => 'Company Field',
    'invoice_field' => 'Invoice Field',
    'invoice_surcharge' => 'Invoice Surcharge',
    'custom_task_fields_help' => 'Add a field when creating a task.',
    'custom_project_fields_help' => 'Add a field when creating a project.',
    'custom_expense_fields_help' => 'Add a field when creating an expense.',
    'custom_vendor_fields_help' => 'Add a field when creating a vendor.',
    'messages' => 'Messages',
    'unpaid_invoice' => 'Unpaid Invoice',
    'paid_invoice' => 'Paid Invoice',
    'unapproved_quote' => 'Unapproved Quote',
    'unapproved_proposal' => 'Unapproved Proposal',
    'autofills_city_state' => 'Auto-fills city/state',
    'no_match_found' => 'No match found',
    'password_strength' => 'Password Strength',
    'strength_weak' => 'Weak',
    'strength_good' => 'Good',
    'strength_strong' => 'Strong',
    'mark' => 'Mark',
    'updated_task_status' => 'Successfully update task status',
    'background_image' => 'Background Image',
    'background_image_help' => 'Use the :link to manage your images, we recommend using a small file.',
    'proposal_editor' => 'proposal editor',
    'background' => 'Background',
    'guide' => 'Guide',
    'gateway_fee_item' => 'Gateway Fee Item',
    'gateway_fee_description' => 'Gateway Fee Surcharge',
    'gateway_fee_discount_description' => 'Gateway Fee Discount',
    'show_payments' => 'Show Payments',
    'show_aging' => 'Show Aging',
    'reference' => 'Reference',
    'amount_paid' => 'Amount Paid',
    'send_notifications_for' => 'Send Notifications For',
    'all_invoices' => 'All Invoices',
    'my_invoices' => 'My Invoices',
    'payment_reference' => 'Payment Reference',
    'maximum' => 'Maximum',
    'sort' => 'Sort',
    'refresh_complete' => 'Refresh Complete',
    'please_enter_your_email' => 'Please enter your email',
    'please_enter_your_password' => 'Please enter your password',
    'please_enter_your_url' => 'Please enter your URL',
    'please_enter_a_product_key' => 'Please enter a product key',
    'an_error_occurred' => 'An error occurred',
    'overview' => 'Overview',
    'copied_to_clipboard' => 'Copied :value to the clipboard',
    'error' => 'Error',
    'could_not_launch' => 'Could not launch',
    'additional' => 'Additional',
    'ok' => 'Ok',
    'email_is_invalid' => 'Email is invalid',
    'items' => 'Items',
    'partial_deposit' => 'Partial/Deposit',
    'add_item' => 'Add Item',
    'total_amount' => 'Total Amount',
    'pdf' => 'PDF',
    'invoice_status_id' => 'Invoice Status',
    'click_plus_to_add_item' => 'Click + to add an item',
    'count_selected' => ':count selected',
    'dismiss' => 'Dismiss',
    'please_select_a_date' => 'Please select a date',
    'please_select_a_client' => 'Please select a client',
    'language' => 'Language',
    'updated_at' => 'Updated',
    'please_enter_an_invoice_number' => 'Please enter an invoice number',
    'please_enter_a_quote_number' => 'Please enter a quote number',
    'clients_invoices' => ':client\'s invoices',
    'viewed' => 'Viewed',
    'approved' => 'Approved',
    'invoice_status_1' => 'Draft',
    'invoice_status_2' => 'Sent',
    'invoice_status_3' => 'Viewed',
    'invoice_status_4' => 'Approved',
    'invoice_status_5' => 'Partial',
    'invoice_status_6' => 'Paid',
    'marked_invoice_as_sent' => 'Successfully marked invoice as sent',
    'please_enter_a_client_or_contact_name' => 'Please enter a client or contact name',
    'restart_app_to_apply_change' => 'Restart the app to apply the change',
    'refresh_data' => 'Refresh Data',
    'blank_contact' => 'Blank Contact',
    'no_records_found' => 'No records found',
    'industry' => 'Industry',
    'size' => 'Size',
    'net' => 'Net',
    'show_tasks' => 'Show tasks',
    'email_reminders' => 'Email Reminders',
    'reminder1' => 'First Reminder',
    'reminder2' => 'Second Reminder',
    'reminder3' => 'Third Reminder',
    'send' => 'Send',
    'auto_billing' => 'Auto billing',
    'button' => 'Button',
    'more' => 'More',
    'edit_recurring_invoice' => 'Edit Recurring Invoice',
    'edit_recurring_quote' => 'Edit Recurring Quote',
    'quote_status' => 'Quote Status',
    'please_select_an_invoice' => 'Please select an invoice',
    'filtered_by' => 'Filtered by',
    'payment_status' => 'Payment Status',
    'payment_status_1' => 'Pending',
    'payment_status_2' => 'Voided',
    'payment_status_3' => 'Failed',
    'payment_status_4' => 'Completed',
    'payment_status_5' => 'Partially Refunded',
    'payment_status_6' => 'Refunded',
    'send_receipt_to_client' => 'Send receipt to the client',
    'refunded' => 'Refunded',
    'marked_quote_as_sent' => 'Successfully marked quote as sent',
    'custom_module_settings' => 'Custom Module Settings',
    'open' => 'Open',
    'new' => 'New',
    'closed' => 'Closed',
    'reopened' => 'Reopened',
    'priority' => 'Priority',
    'last_updated' => 'Last Updated',
    'comment' => 'Comments',
    'tags' => 'Tags',
    'linked_objects' => 'Linked Objects',
    'low' => 'Low',
    'medium' => 'Medium',
    'high' => 'High',
    'no_due_date' => 'No due date set',
    'assigned_to' => 'Assigned to',
    'reply' => 'Reply',
    'awaiting_reply' => 'Awaiting reply',
    'mark_spam' => 'Mark as Spam',
    'local_part' => 'Local Part',
    'local_part_unavailable' => 'Name taken',
    'local_part_available' => 'Name available',
    'local_part_invalid' => 'Invalid name (alpha numeric only, no spaces',
    'local_part_help' => 'Customize the local part of your inbound support email, ie. <EMAIL>',
    'from_name_help' => 'From name is the recognizable sender which is displayed instead of the email address, ie Support Center',
    'local_part_placeholder' => 'YOUR_NAME',
    'from_name_placeholder' => 'Support Center',
    'attachments' => 'Attachments',
    'client_upload' => 'Client uploads',
    'enable_client_upload_help' => 'Allow clients to upload documents/attachments',
    'max_file_size_help' => 'Maximum file size (KB) is limited by your post_max_size and upload_max_filesize variables as set in your PHP.INI',
    'max_file_size' => 'Maximum file size',
    'mime_types' => 'Mime types',
    'mime_types_placeholder' => '.pdf , .docx, .jpg',
    'mime_types_help' => 'Comma separated list of allowed mime types, leave blank for all',
    'ticket_number_start_help' => 'Ticket number must be greater than the current ticket number',
    'new_ticket_template_id' => 'New ticket',
    'new_ticket_autoresponder_help' => 'Selecting a template will send an auto response to a client/contact when a new ticket is created',
    'update_ticket_template_id' => 'Updated ticket',
    'update_ticket_autoresponder_help' => 'Selecting a template will send an auto response to a client/contact when a ticket is updated',
    'close_ticket_template_id' => 'Closed ticket',
    'close_ticket_autoresponder_help' => 'Selecting a template will send an auto response to a client/contact when a ticket is closed',
    'default_priority' => 'Default priority',
    'alert_new_comment_id' => 'New comment',
    'update_ticket_notification_list' => 'Additional new comment notifications',
    'comma_separated_values' => '<EMAIL>, <EMAIL>',
    'default_agent' => 'Default Agent',
    'default_agent_help' => 'If selected will automatically be assigned to all inbound tickets',
    'show_agent_details' => 'Show agent details on responses',
    'avatar' => 'Avatar',
    'remove_avatar' => 'Remove avatar',
    'add_template' => 'Add Template',
    'archive_ticket_template' => 'Archive Template',
    'restore_ticket_template' => 'Restore Template',
    'archived_ticket_template' => 'Successfully archived template',
    'restored_ticket_template' => 'Successfully restored template',
    'enter_ticket_message' => 'Please enter a message to update the ticket',
    'show_hide_all' => 'Show / Hide all',
    'subject_required' => 'Subject required',
    'mobile_refresh_warning' => 'If you\'re using the mobile app you may need to do a full refresh.',
    'merge' => 'Merge',
    'merged' => 'Merged',
    'agent' => 'Agent',
    'include_in_filter' => 'Include in filter',
    'custom_client1' => ':VALUE',
    'custom_client2' => ':VALUE',
    'compare' => 'Compare',
    'hosted_login' => 'Hosted Login',
    'selfhost_login' => 'Selfhost Login',
    'google_login' => 'Google Login',
    'thanks_for_patience' => 'Thank for your patience while we work to implement these features.<br><br>We hope to have them completed in the next few months.<br><br>Until then we\'ll continue to support the',
    'legacy_mobile_app' => 'legacy mobile app',
    'today' => 'Today',
    'current' => 'Current',
    'previous' => 'Previous',
    'current_period' => 'Current Period',
    'comparison_period' => 'Comparison Period',
    'previous_period' => 'Previous Period',
    'previous_year' => 'Previous Year',
    'compare_to' => 'Compare to',
    'last_week' => 'Last Week',
    'clone_to_invoice' => 'Clone to Invoice',
    'clone_to_quote' => 'Clone to Quote',
    'convert' => 'Convert',
    'last7_days' => 'Last 7 Days',
    'last30_days' => 'Last 30 Days',
    'custom_js' => 'Custom JS',
    'adjust_fee_percent_help' => 'Adjust percent to account for fee',
    'show_product_notes' => 'Show product details',
    'show_product_notes_help' => 'Include the <b>description and cost</b> in the product dropdown',
    'important' => 'Important',
    'thank_you_for_using_our_app' => 'Thank you for using our app!',
    'if_you_like_it' => 'If you like it please',
    'to_rate_it' => 'to rate it.',
    'average' => 'Average',
    'unapproved' => 'Unapproved',
    'authenticate_to_change_setting' => 'Please authenticate to change this setting',
    'locked' => 'Locked',
    'authenticate' => 'Authenticate',
    'please_authenticate' => 'Please authenticate',
    'biometric_authentication' => 'Biometric Authentication',
    'auto_start_tasks' => 'Auto Start Tasks',
    'budgeted' => 'Budgeted',
    'please_enter_a_name' => 'Please enter a name',
    'click_plus_to_add_time' => 'Click + to add time',
    'design' => 'Design',
    'password_is_too_short' => 'Password is too short',
    'failed_to_find_record' => 'Failed to find record',
    'valid_until_days' => 'Valid Until',
    'valid_until_days_help' => 'Automatically sets the <b>Valid Until</b> value on quotes to this many days in the future.  Leave blank to disable.',
    'usually_pays_in_days' => 'Days',
    'requires_an_enterprise_plan' => 'Requires an Enterprise Plan',
    'take_picture' => 'Take Picture',
    'upload_file' => 'Upload File',
    'new_document' => 'New Document',
    'edit_document' => 'Edit Document',
    'uploaded_document' => 'Successfully uploaded document',
    'updated_document' => 'Successfully updated document',
    'archived_document' => 'Successfully archived document',
    'deleted_document' => 'Successfully deleted document',
    'restored_document' => 'Successfully restored document',
    'no_history' => 'No History',
    'expense_status_1' => 'Logged',
    'expense_status_2' => 'Pending',
    'expense_status_3' => 'Invoiced',
    'no_record_selected' => 'No record selected',
    'error_unsaved_changes' => 'Please save or cancel your changes',
    'thank_you_for_your_purchase' => 'Thank you for your purchase!',
    'redeem' => 'Redeem',
    'back' => 'Back',
    'past_purchases' => 'Past Purchases',
    'annual_subscription' => 'Annual Subscription',
    'pro_plan' => 'Pro Plan',
    'enterprise_plan' => 'Enterprise Plan',
    'count_users' => ':count users',
    'upgrade' => 'Upgrade',
    'please_enter_a_first_name' => 'Please enter a first name',
    'please_enter_a_last_name' => 'Please enter a last name',
    'please_agree_to_terms_and_privacy' => 'Please agree to the terms of service and privacy policy to create an account.',
    'i_agree_to_the' => 'I agree to the',
    'terms_of_service_link' => 'terms of service',
    'privacy_policy_link' => 'privacy policy',
    'view_website' => 'View Website',
    'create_account' => 'Create Account',
    'email_login' => 'Email Login',
    'late_fees' => 'Late Fees',
    'payment_number' => 'Payment Number',
    'before_due_date' => 'Before the due date',
    'after_due_date' => 'After the due date',
    'after_invoice_date' => 'After the invoice date',
    'filtered_by_user' => 'Filtered by User',
    'created_user' => 'Successfully created user',
    'primary_font' => 'Primary Font',
    'secondary_font' => 'Secondary Font',
    'number_padding' => 'Number Padding',
    'general' => 'General',
    'surcharge_field' => 'Surcharge Field',
    'company_value' => 'Company Value',
    'credit_field' => 'Credit Field',
    'payment_field' => 'Payment Field',
    'group_field' => 'Group Field',
    'number_counter' => 'Number Counter',
    'number_pattern' => 'Number Pattern',
    'custom_javascript' => 'Custom JavaScript',
    'portal_mode' => 'Portal Mode',
    'attach_pdf' => 'Attach PDF',
    'attach_documents' => 'Attach Documents',
    'attach_ubl' => 'Attach UBL/E-Invoice',
    'email_style' => 'Email Style',
    'processed' => 'Processed',
    'fee_amount' => 'Fee Amount',
    'fee_percent' => 'Fee Percent',
    'fee_cap' => 'Fee Cap',
    'limits_and_fees' => 'Limits/Fees',
    'credentials' => 'Credentials',
    'require_billing_address_help' => 'Require client to provide their billing address',
    'require_shipping_address_help' => 'Require client to provide their shipping address',
    'deleted_tax_rate' => 'Successfully deleted tax rate',
    'restored_tax_rate' => 'Successfully restored tax rate',
    'provider' => 'Provider',
    'company_gateway' => 'Payment Gateway',
    'company_gateways' => 'Payment Gateways',
    'new_company_gateway' => 'New Gateway',
    'edit_company_gateway' => 'Edit Gateway',
    'created_company_gateway' => 'Successfully created gateway',
    'updated_company_gateway' => 'Successfully updated gateway',
    'archived_company_gateway' => 'Successfully archived gateway',
    'deleted_company_gateway' => 'Successfully deleted gateway',
    'restored_company_gateway' => 'Successfully restored gateway',
    'continue_editing' => 'Continue Editing',
    'default_value' => 'Default value',
    'currency_format' => 'Currency Format',
    'first_day_of_the_week' => 'First Day of the Week',
    'first_month_of_the_year' => 'First Month of the Year',
    'symbol' => 'Symbol',
    'ocde' => 'Code',
    'date_format' => 'Date Format',
    'datetime_format' => 'Datetime Format',
    'send_reminders' => 'Send Reminders',
    'timezone' => 'Timezone',
    'filtered_by_group' => 'Filtered by Group',
    'filtered_by_invoice' => 'Filtered by Invoice',
    'filtered_by_client' => 'Filtered by Client',
    'filtered_by_vendor' => 'Filtered by Vendor',
    'group_settings' => 'Group Settings',
    'groups' => 'Groups',
    'new_group' => 'New Group',
    'edit_group' => 'Edit Group',
    'created_group' => 'Successfully created group',
    'updated_group' => 'Successfully updated group',
    'archived_group' => 'Successfully archived group',
    'deleted_group' => 'Successfully deleted group',
    'restored_group' => 'Successfully restored group',
    'upload_logo' => 'Upload Your Company Logo',
    'uploaded_logo' => 'Successfully uploaded logo',
    'saved_settings' => 'Successfully saved settings',
    'device_settings' => 'Device Settings',
    'credit_cards_and_banks' => 'Credit Cards & Banks',
    'price' => 'Price',
    'email_sign_up' => 'Email Sign Up',
    'google_sign_up' => 'Google Sign Up',
    'sign_up_with_google' => 'Sign Up With Google',
    'long_press_multiselect' => 'Long-press Multiselect',
    'migrate_to_next_version' => 'Migrate to the next version of Invoice Ninja',
    'migrate_intro_text' => 'We\'ve been working on next version of Invoice Ninja. Click the button bellow to start the migration.',
    'start_the_migration' => 'Start the migration',
    'migration' => 'Migration',
    'welcome_to_the_new_version' => 'Welcome to the new version of Invoice Ninja',
    'next_step_data_download' => 'At the next step, we\'ll let you download your data for the migration.',
    'download_data' => 'Press button below to download the data.',
    'continue' => 'Continue',
    'company1' => 'Custom Company 1',
    'company2' => 'Custom Company 2',
    'company3' => 'Custom Company 3',
    'company4' => 'Custom Company 4',
    'product1' => 'Custom Product 1',
    'product2' => 'Custom Product 2',
    'product3' => 'Custom Product 3',
    'product4' => 'Custom Product 4',
    'client1' => 'Custom Client 1',
    'client2' => 'Custom Client 2',
    'client3' => 'Custom Client 3',
    'client4' => 'Custom Client 4',
    'contact1' => 'Custom Contact 1',
    'contact2' => 'Custom Contact 2',
    'contact3' => 'Custom Contact 3',
    'contact4' => 'Custom Contact 4',
    'task1' => 'Custom Task 1',
    'task2' => 'Custom Task 2',
    'task3' => 'Custom Task 3',
    'task4' => 'Custom Task 4',
    'project1' => 'Custom Project 1',
    'project2' => 'Custom Project 2',
    'project3' => 'Custom Project 3',
    'project4' => 'Custom Project 4',
    'expense1' => 'Custom Expense 1',
    'expense2' => 'Custom Expense 2',
    'expense3' => 'Custom Expense 3',
    'expense4' => 'Custom Expense 4',
    'vendor1' => 'Custom Vendor 1',
    'vendor2' => 'Custom Vendor 2',
    'vendor3' => 'Custom Vendor 3',
    'vendor4' => 'Custom Vendor 4',
    'invoice1' => 'Custom Invoice 1',
    'invoice2' => 'Custom Invoice 2',
    'invoice3' => 'Custom Invoice 3',
    'invoice4' => 'Custom Invoice 4',
    'payment1' => 'Custom Payment 1',
    'payment2' => 'Custom Payment 2',
    'payment3' => 'Custom Payment 3',
    'payment4' => 'Custom Payment 4',
    'surcharge1' => 'Custom Surcharge 1',
    'surcharge2' => 'Custom Surcharge 2',
    'surcharge3' => 'Custom Surcharge 3',
    'surcharge4' => 'Custom Surcharge 4',
    'group1' => 'Custom Group 1',
    'group2' => 'Custom Group 2',
    'group3' => 'Custom Group 3',
    'group4' => 'Custom Group 4',
    'number' => 'Number',
    'count' => 'Count',
    'is_active' => 'Is Active',
    'contact_last_login' => 'Contact Last Login',
    'contact_full_name' => 'Contact Full Name',
    'contact_custom_value1' => 'Contact Custom Value 1',
    'contact_custom_value2' => 'Contact Custom Value 2',
    'contact_custom_value3' => 'Contact Custom Value 3',
    'contact_custom_value4' => 'Contact Custom Value 4',
    'assigned_to_id' => 'Assigned To Id',
    'created_by_id' => 'Created By Id',
    'add_column' => 'Add Column',
    'edit_columns' => 'Edit Columns',
    'to_learn_about_gogle_fonts' => 'to learn about Google Fonts',
    'refund_date' => 'Refund Date',
    'multiselect' => 'Multiselect',
    'verify_password' => 'Verify Password',
    'applied' => 'Applied',
    'include_recent_errors' => 'Include recent errors from the logs',
    'your_message_has_been_received' => 'We have received your message and will try to respond promptly.',
    'show_product_details' => 'Show Product Details',
    'show_product_details_help' => 'Include the description and cost in the product dropdown',
    'pdf_min_requirements' => 'The PDF renderer requires :version',
    'adjust_fee_percent' => 'Adjust Fee Percent',
    'configure_settings' => 'Configure Settings',
    'about' => 'About',
    'credit_email' => 'Credit Email',
    'domain_url' => 'Domain URL',
    'password_is_too_easy' => 'Password must contain an upper case character and a number',
    'client_portal_tasks' => 'Client Portal Tasks',
    'client_portal_dashboard' => 'Client Portal Dashboard',
    'please_enter_a_value' => 'Please enter a value',
    'deleted_logo' => 'Successfully deleted logo',
    'generate_number' => 'Generate Number',
    'when_saved' => 'When Saved',
    'when_sent' => 'When Sent',
    'select_company' => 'Select Company',
    'float' => 'Float',
    'collapse' => 'Collapse',
    'show_or_hide' => 'Show/hide',
    'menu_sidebar' => 'Menu Sidebar',
    'history_sidebar' => 'History Sidebar',
    'tablet' => 'Tablet',
    'layout' => 'Layout',
    'module' => 'Module',
    'first_custom' => 'First Custom',
    'second_custom' => 'Second Custom',
    'third_custom' => 'Third Custom',
    'show_cost' => 'Show Cost',
    'show_cost_help' => 'Display a product cost field to track the markup/profit',
    'show_product_quantity' => 'Show Product Quantity',
    'show_product_quantity_help' => 'Display a product quantity field, otherwise default to one',
    'show_invoice_quantity' => 'Show Invoice Quantity',
    'show_invoice_quantity_help' => 'Display a line item quantity field, otherwise default to one',
    'default_quantity' => 'Default Quantity',
    'default_quantity_help' => 'Automatically set the line item quantity to one',
    'one_tax_rate' => 'One Tax Rate',
    'two_tax_rates' => 'Two Tax Rates',
    'three_tax_rates' => 'Three Tax Rates',
    'default_tax_rate' => 'Default Tax Rate',
    'invoice_tax' => 'Invoice Tax',
    'line_item_tax' => 'Line Item Tax',
    'inclusive_taxes' => 'Inclusive Taxes',
    'invoice_tax_rates' => 'Invoice Tax Rates',
    'item_tax_rates' => 'Item Tax Rates',
    'configure_rates' => 'Configure rates',
    'tax_settings_rates' => 'Tax Rates',
    'accent_color' => 'Accent Color',
    'comma_sparated_list' => 'Comma separated list',
    'single_line_text' => 'Single-line text',
    'multi_line_text' => 'Multi-line text',
    'dropdown' => 'Dropdown',
    'field_type' => 'Field Type',
    'recover_password_email_sent' => 'A password recovery email has been sent',
    'removed_user' => 'Successfully removed user',
    'freq_three_years' => 'Three Years',
    'military_time_help' => '24 Hour Display',
    'click_here_capital' => 'Click here',
    'marked_invoice_as_paid' => 'Successfully marked invoice as paid',
    'marked_invoices_as_sent' => 'Successfully marked invoices as sent',
    'marked_invoices_as_paid' => 'Successfully marked invoices as paid',
    'activity_57' => 'System failed to email invoice :invoice',
    'custom_value3' => 'Custom Value 3',
    'custom_value4' => 'Custom Value 4',
    'email_style_custom' => 'Custom Email Style',
    'custom_message_dashboard' => 'Custom Dashboard Message',
    'custom_message_unpaid_invoice' => 'Custom Unpaid Invoice Message',
    'custom_message_paid_invoice' => 'Custom Paid Invoice Message',
    'custom_message_unapproved_quote' => 'Custom Unapproved Quote Message',
    'lock_sent_invoices' => 'Lock Sent Invoices',
    'translations' => 'Translations',
    'task_number_pattern' => 'Task Number Pattern',
    'task_number_counter' => 'Task Number Counter',
    'expense_number_pattern' => 'Expense Number Pattern',
    'expense_number_counter' => 'Expense Number Counter',
    'vendor_number_pattern' => 'Vendor Number Pattern',
    'vendor_number_counter' => 'Vendor Number Counter',
    'ticket_number_pattern' => 'Ticket Number Pattern',
    'ticket_number_counter' => 'Ticket Number Counter',
    'payment_number_pattern' => 'Payment Number Pattern',
    'payment_number_counter' => 'Payment Number Counter',
    'invoice_number_pattern' => 'Invoice Number Pattern',
    'quote_number_pattern' => 'Quote Number Pattern',
    'client_number_pattern' => 'Credit Number Pattern',
    'client_number_counter' => 'Credit Number Counter',
    'credit_number_pattern' => 'Credit Number Pattern',
    'credit_number_counter' => 'Credit Number Counter',
    'reset_counter_date' => 'Reset Counter Date',
    'counter_padding' => 'Counter Padding',
    'shared_invoice_quote_counter' => 'Share Invoice/Quote Counter',
    'default_tax_name_1' => 'Default Tax Name 1',
    'default_tax_rate_1' => 'Default Tax Rate 1',
    'default_tax_name_2' => 'Default Tax Name 2',
    'default_tax_rate_2' => 'Default Tax Rate 2',
    'default_tax_name_3' => 'Default Tax Name 3',
    'default_tax_rate_3' => 'Default Tax Rate 3',
    'email_subject_invoice' => 'Email Invoice Subject',
    'email_subject_quote' => 'Email Quote Subject',
    'email_subject_payment' => 'Email Payment Subject',
    'switch_list_table' => 'Switch List Table',
    'client_city' => 'Client City',
    'client_state' => 'Client State',
    'client_country' => 'Client Country',
    'client_is_active' => 'Client is Active',
    'client_balance' => 'Client Balance',
    'client_address1' => 'Client Street',
    'client_address2' => 'Client Apt/Suite',
    'client_shipping_address1' => 'Client Shipping Street',
    'client_shipping_address2' => 'Client Shipping Apt/Suite',
    'tax_rate1' => 'Tax Rate 1',
    'tax_rate2' => 'Tax Rate 2',
    'tax_rate3' => 'Tax Rate 3',
    'archived_at' => 'Archived At',
    'has_expenses' => 'Has Expenses',
    'custom_taxes1' => 'Custom Taxes 1',
    'custom_taxes2' => 'Custom Taxes 2',
    'custom_taxes3' => 'Custom Taxes 3',
    'custom_taxes4' => 'Custom Taxes 4',
    'custom_surcharge1' => 'Custom Surcharge 1',
    'custom_surcharge2' => 'Custom Surcharge 2',
    'custom_surcharge3' => 'Custom Surcharge 3',
    'custom_surcharge4' => 'Custom Surcharge 4',
    'is_deleted' => 'Is Deleted',
    'vendor_city' => 'Vendor City',
    'vendor_state' => 'Vendor State',
    'vendor_country' => 'Vendor Country',
    'credit_footer' => 'Credit Footer',
    'credit_terms' => 'Credit Terms',
    'untitled_company' => 'Untitled Company',
    'added_company' => 'Successfully added company',
    'supported_events' => 'Supported Events',
    'custom3' => 'Third Custom',
    'custom4' => 'Fourth Custom',
    'optional' => 'Optional',
    'license' => 'License',
    'invoice_balance' => 'Invoice Balance',
    'saved_design' => 'Successfully saved design',
    'client_details' => 'Client Details',
    'company_address' => 'Company Address',
    'quote_details' => 'Quote Details',
    'credit_details' => 'Credit Details',
    'product_columns' => 'Product Columns',
    'task_columns' => 'Task Columns',
    'add_field' => 'Add Field',
    'all_events' => 'All Events',
    'owned' => 'Owned',
    'payment_success' => 'Payment Success',
    'payment_failure' => 'Payment Failure',
    'quote_sent' => 'Quote Sent',
    'credit_sent' => 'Credit Sent',
    'invoice_viewed' => 'Invoice Viewed',
    'quote_viewed' => 'Quote Viewed',
    'credit_viewed' => 'Credit Viewed',
    'quote_approved' => 'Quote Approved',
    'receive_all_notifications' => 'Receive All Notifications',
    'purchase_license' => 'Purchase License',
    'enable_modules' => 'Enable Modules',
    'converted_quote' => 'Successfully converted quote',
    'credit_design' => 'Credit Design',
    'includes' => 'Includes',
    'css_framework' => 'CSS Framework',
    'custom_designs' => 'Custom Designs',
    'designs' => 'Designs',
    'new_design' => 'New Design',
    'edit_design' => 'Edit Design',
    'created_design' => 'Successfully created design',
    'updated_design' => 'Successfully updated design',
    'archived_design' => 'Successfully archived design',
    'deleted_design' => 'Successfully deleted design',
    'removed_design' => 'Successfully removed design',
    'restored_design' => 'Successfully restored design',
    'recurring_tasks' => 'Recurring Tasks',
    'removed_credit' => 'Successfully removed credit',
    'latest_version' => 'Latest Version',
    'update_now' => 'Update Now',
    'a_new_version_is_available' => 'A new version of the web app is available',
    'update_available' => 'Update Available',
    'app_updated' => 'Update successfully completed',
    'integrations' => 'Integrations',
    'tracking_id' => 'Tracking Id',
    'slack_webhook_url' => 'Slack Webhook URL',
    'partial_payment' => 'Partial Payment',
    'partial_payment_email' => 'Partial Payment Email',
    'clone_to_credit' => 'Clone to Credit',
    'emailed_credit' => 'Successfully emailed credit',
    'marked_credit_as_sent' => 'Successfully marked credit as sent',
    'email_subject_payment_partial' => 'Email Partial Payment Subject',
    'is_approved' => 'Is Approved',
    'migration_went_wrong' => 'Oops, something went wrong! Please make sure you have setup an Invoice Ninja v5 instance before starting the migration.',
    'cross_migration_message' => 'Cross account migration is not allowed. Please read more about it here: <a href="https://invoiceninja.github.io/docs/migration/#troubleshooting">https://invoiceninja.github.io/docs/migration/#troubleshooting</a>',
    'email_credit' => 'Email Credit',
    'client_email_not_set' => 'Client does not have an email address set',
    'ledger' => 'Ledger',
    'view_pdf' => 'View PDF',
    'all_records' => 'All records',
    'owned_by_user' => 'Owned by user',
    'credit_remaining' => 'Credit Remaining',
    'use_default' => 'Use default',
    'reminder_endless' => 'Endless Reminders',
    'number_of_days' => 'Number of days',
    'configure_payment_terms' => 'Configure Payment Terms',
    'payment_term' => 'Payment Term',
    'new_payment_term' => 'New Payment Term',
    'deleted_payment_term' => 'Successfully deleted payment term',
    'removed_payment_term' => 'Successfully removed payment term',
    'restored_payment_term' => 'Successfully restored payment term',
    'full_width_editor' => 'Full Width Editor',
    'full_height_filter' => 'Full Height Filter',
    'email_sign_in' => 'Sign in with email',
    'change' => 'Change',
    'change_to_mobile_layout' => 'Change to the mobile layout?',
    'change_to_desktop_layout' => 'Change to the desktop layout?',
    'send_from_gmail' => 'Send from Gmail',
    'reversed' => 'Reversed',
    'cancelled' => 'Cancelled',
    'quote_amount' => 'Quote Amount',
    'hosted' => 'Hosted',
    'selfhosted' => 'Self-Hosted',
    'hide_menu' => 'Hide Menu',
    'show_menu' => 'Show Menu',
    'partially_refunded' => 'Partially Refunded',
    'search_documents' => 'Search Documents',
    'search_designs' => 'Search Designs',
    'search_invoices' => 'Search Invoices',
    'search_clients' => 'Search Clients',
    'search_products' => 'Search Products',
    'search_quotes' => 'Search Quotes',
    'search_credits' => 'Search Credits',
    'search_vendors' => 'Search Vendors',
    'search_users' => 'Search Users',
    'search_tax_rates' => 'Search Tax Rates',
    'search_tasks' => 'Search Tasks',
    'search_settings' => 'Search Settings',
    'search_projects' => 'Search Projects',
    'search_expenses' => 'Search Expenses',
    'search_payments' => 'Search Payments',
    'search_groups' => 'Search Groups',
    'search_company' => 'Search Company',
    'cancelled_invoice' => 'Successfully cancelled invoice',
    'cancelled_invoices' => 'Successfully cancelled invoices',
    'reversed_invoice' => 'Successfully reversed invoice',
    'reversed_invoices' => 'Successfully reversed invoices',
    'reverse' => 'Reverse',
    'filtered_by_project' => 'Filtered by Project',
    'google_sign_in' => 'Sign in with Google',
    'activity_58' => ':user reversed invoice :invoice',
    'activity_59' => ':user cancelled invoice :invoice',
    'payment_reconciliation_failure' => 'Reconciliation Failure',
    'payment_reconciliation_success' => 'Reconciliation Success',
    'gateway_success' => 'Gateway Success',
    'gateway_failure' => 'Gateway Failure',
    'gateway_error' => 'Gateway Error',
    'email_send' => 'Email Send',
    'email_retry_queue' => 'Email Retry Queue',
    'failure' => 'Failure',
    'quota_exceeded' => 'Quota Exceeded',
    'upstream_failure' => 'Upstream Failure',
    'system_logs' => 'System Logs',
    'copy_link' => 'Copy Link',
    'welcome_to_invoice_ninja' => 'Welcome to Invoice Ninja',
    'optin' => 'Opt-In',
    'optout' => 'Opt-Out',
    'auto_convert' => 'Auto Convert',
    'reminder1_sent' => 'Reminder 1 Sent',
    'reminder2_sent' => 'Reminder 2 Sent',
    'reminder3_sent' => 'Reminder 3 Sent',
    'reminder_last_sent' => 'Reminder Last Sent',
    'pdf_page_info' => 'Page :current of :total',
    'emailed_credits' => 'Successfully emailed credits',
    'view_in_stripe' => 'View in Stripe',
    'rows_per_page' => 'Rows Per Page',
    'apply_payment' => 'Apply Payment',
    'unapplied' => 'Unapplied',
    'custom_labels' => 'Custom Labels',
    'record_type' => 'Record Type',
    'record_name' => 'Record Name',
    'file_type' => 'File Type',
    'height' => 'Height',
    'width' => 'Width',
    'health_check' => 'Health Check',
    'last_login_at' => 'Last Login At',
    'company_key' => 'Company Key',
    'storefront' => 'Storefront',
    'storefront_help' => 'Enable third-party apps to create invoices',
    'count_records_selected' => ':count records selected',
    'count_record_selected' => ':count record selected',
    'client_created' => 'Client Created',
    'online_payment_email' => 'Online Payment Email',
    'manual_payment_email' => 'Manual Payment Email',
    'completed' => 'Completed',
    'gross' => 'Gross',
    'net_amount' => 'Net Amount',
    'net_balance' => 'Net Balance',
    'client_settings' => 'Client Settings',
    'selected_invoices' => 'Selected Invoices',
    'selected_payments' => 'Selected Payments',
    'selected_quotes' => 'Selected Quotes',
    'selected_tasks' => 'Selected Tasks',
    'selected_expenses' => 'Selected Expenses',
    'past_due_invoices' => 'Past Due Invoices',
    'create_payment' => 'Create Payment',
    'update_quote' => 'Update Quote',
    'update_invoice' => 'Update Invoice',
    'update_client' => 'Update Client',
    'update_vendor' => 'Update Vendor',
    'create_expense' => 'Create Expense',
    'update_expense' => 'Update Expense',
    'update_task' => 'Update Task',
    'approve_quote' => 'Approve Quote',
    'when_paid' => 'When Paid',
    'expires_on' => 'Expires On',
    'show_sidebar' => 'Show Sidebar',
    'hide_sidebar' => 'Hide Sidebar',
    'event_type' => 'Event Type',
    'copy' => 'Copy',
    'must_be_online' => 'Please restart the app once connected to the internet',
    'crons_not_enabled' => 'The crons need to be enabled',
    'api_webhooks' => 'API Webhooks',
    'search_webhooks' => 'Search :count Webhooks',
    'search_webhook' => 'Search 1 Webhook',
    'webhook' => 'Webhook',
    'webhooks' => 'Webhooks',
    'new_webhook' => 'New Webhook',
    'edit_webhook' => 'Edit Webhook',
    'created_webhook' => 'Successfully created webhook',
    'updated_webhook' => 'Successfully updated webhook',
    'archived_webhook' => 'Successfully archived webhook',
    'deleted_webhook' => 'Successfully deleted webhook',
    'removed_webhook' => 'Successfully removed webhook',
    'restored_webhook' => 'Successfully restored webhook',
    'search_tokens' => 'Search :count Tokens',
    'search_token' => 'Search 1 Token',
    'new_token' => 'New Token',
    'removed_token' => 'Successfully removed token',
    'restored_token' => 'Successfully restored token',
    'client_registration' => 'Client Registration',
    'client_registration_help' => 'Enable clients to self register in the portal',
    'customize_and_preview' => 'Customize & Preview',
    'search_document' => 'Search 1 Document',
    'search_design' => 'Search 1 Design',
    'search_invoice' => 'Search 1 Invoice',
    'search_client' => 'Search 1 Client',
    'search_product' => 'Search 1 Product',
    'search_quote' => 'Search 1 Quote',
    'search_credit' => 'Search 1 Credit',
    'search_vendor' => 'Search 1 Vendor',
    'search_user' => 'Search 1 User',
    'search_tax_rate' => 'Search 1 Tax Rate',
    'search_task' => 'Search 1 Tasks',
    'search_project' => 'Search 1 Project',
    'search_expense' => 'Search 1 Expense',
    'search_payment' => 'Search 1 Payment',
    'search_group' => 'Search 1 Group',
    'created_on' => 'Created On',
    'payment_status_-1' => 'Unapplied',
    'lock_invoices' => 'Lock Invoices',
    'show_table' => 'Show Table',
    'show_list' => 'Show List',
    'view_changes' => 'View Changes',
    'force_update' => 'Force Update',
    'force_update_help' => 'You are running the latest version but there may be pending fixes available.',
    'mark_paid_help' => 'Track the expense has been paid',
    'mark_invoiceable_help' => 'Enable the expense to be invoiced',
    'add_documents_to_invoice_help' => 'Make the documents visible to client',
    'convert_currency_help' => 'Set an exchange rate',
    'expense_settings' => 'Expense Settings',
    'clone_to_recurring' => 'Clone to Recurring',
    'crypto' => 'Crypto',
    'user_field' => 'User Field',
    'variables' => 'Variables',
    'show_password' => 'Show Password',
    'hide_password' => 'Hide Password',
    'copy_error' => 'Copy Error',
    'capture_card' => 'Capture Card',
    'auto_bill_enabled' => 'Auto Bill Enabled',
    'total_taxes' => 'Total Taxes',
    'line_taxes' => 'Line Taxes',
    'total_fields' => 'Total Fields',
    'stopped_recurring_invoice' => 'Successfully stopped recurring invoice',
    'started_recurring_invoice' => 'Successfully started recurring invoice',
    'resumed_recurring_invoice' => 'Successfully resumed recurring invoice',
    'gateway_refund' => 'Gateway Refund',
    'gateway_refund_help' => 'Process the refund with the payment gateway',
    'due_date_days' => 'Due Date',
    'paused' => 'Paused',
    'day_count' => 'Day :count',
    'first_day_of_the_month' => 'First Day of the Month',
    'last_day_of_the_month' => 'Last Day of the Month',
    'use_payment_terms' => 'Use Payment Terms',
    'endless' => 'Endless',
    'next_send_date' => 'Next Send Date',
    'remaining_cycles' => 'Remaining Cycles',
    'created_recurring_invoice' => 'Successfully created recurring invoice',
    'updated_recurring_invoice' => 'Successfully updated recurring invoice',
    'removed_recurring_invoice' => 'Successfully removed recurring invoice',
    'search_recurring_invoice' => 'Search 1 Recurring Invoice',
    'search_recurring_invoices' => 'Search :count Recurring Invoices',
    'send_date' => 'Send Date',
    'auto_bill_on' => 'Auto Bill On',
    'minimum_under_payment_amount' => 'Minimum Under Payment Amount',
    'allow_over_payment' => 'Allow Overpayment',
    'allow_over_payment_help' => 'Support paying extra to accept tips',
    'allow_under_payment' => 'Allow Underpayment',
    'allow_under_payment_help' => 'Support paying at minimum the partial/deposit amount',
    'test_mode' => 'Test Mode',
    'calculated_rate' => 'Calculated Rate',
    'default_task_rate' => 'Default Task Rate',
    'clear_cache' => 'Clear Cache',
    'sort_order' => 'Sort Order',
    'task_status' => 'Status',
    'task_statuses' => 'Task Statuses',
    'new_task_status' => 'New Task Status',
    'edit_task_status' => 'Edit Task Status',
    'created_task_status' => 'Successfully created task status',
    'archived_task_status' => 'Successfully archived task status',
    'deleted_task_status' => 'Successfully deleted task status',
    'removed_task_status' => 'Successfully removed task status',
    'restored_task_status' => 'Successfully restored task status',
    'search_task_status' => 'Search 1 Task Status',
    'search_task_statuses' => 'Search :count Task Statuses',
    'show_tasks_table' => 'Show Tasks Table',
    'show_tasks_table_help' => 'Always show the tasks section when creating invoices',
    'invoice_task_timelog' => 'Invoice Task Timelog',
    'invoice_task_timelog_help' => 'Add time details to the invoice line items',
    'auto_start_tasks_help' => 'Start tasks before saving',
    'configure_statuses' => 'Configure Statuses',
    'task_settings' => 'Task Settings',
    'configure_categories' => 'Configure Categories',
    'edit_expense_category' => 'Edit Expense Category',
    'removed_expense_category' => 'Successfully removed expense category',
    'search_expense_category' => 'Search 1 Expense Category',
    'search_expense_categories' => 'Search :count Expense Categories',
    'use_available_credits' => 'Use Available Credits',
    'show_option' => 'Show Option',
    'negative_payment_error' => 'The credit amount cannot exceed the payment amount',
    'should_be_invoiced_help' => 'Enable the expense to be invoiced',
    'configure_gateways' => 'Configure Gateways',
    'payment_partial' => 'Partial Payment',
    'is_running' => 'Is Running',
    'invoice_currency_id' => 'Invoice Currency ID',
    'tax_name1' => 'Tax Name 1',
    'tax_name2' => 'Tax Name 2',
    'transaction_id' => 'Transaction ID',
    'invoice_late' => 'Invoice Late',
    'quote_expired' => 'Quote Expired',
    'recurring_invoice_total' => 'Invoice Total',
    'actions' => 'Actions',
    'expense_number' => 'Expense Number',
    'task_number' => 'Task Number',
    'project_number' => 'Project Number',
    'view_settings' => 'View Settings',
    'company_disabled_warning' => 'Warning: this company has not yet been activated',
    'late_invoice' => 'Late Invoice',
    'expired_quote' => 'Expired Quote',
    'remind_invoice' => 'Remind Invoice',
    'client_phone' => 'Client Phone',
    'required_fields' => 'Required Fields',
    'enabled_modules' => 'Enabled Modules',
    'activity_60' => ':contact viewed quote :quote',
    'activity_61' => ':user updated client :client',
    'activity_62' => ':user updated vendor :vendor',
    'activity_63' => ':user emailed first reminder for invoice :invoice to :contact',
    'activity_64' => ':user emailed second reminder for invoice :invoice to :contact',
    'activity_65' => ':user emailed third reminder for invoice :invoice to :contact',
    'activity_66' => ':user emailed endless reminder for invoice :invoice to :contact',
    'expense_category_id' => 'Expense Category ID',
    'view_licenses' => 'View Licenses',
    'fullscreen_editor' => 'Fullscreen Editor',
    'sidebar_editor' => 'Sidebar Editor',
    'please_type_to_confirm' => 'Please type ":value" to confirm',
    'purge' => 'Purge',
    'clone_to' => 'Clone To',
    'clone_to_other' => 'Clone to Other',
    'labels' => 'Labels',
    'add_custom' => 'Add Custom',
    'payment_tax' => 'Payment Tax',
    'white_label' => 'White Label',
    'sent_invoices_are_locked' => 'Sent invoices are locked',
    'paid_invoices_are_locked' => 'Paid invoices are locked',
    'source_code' => 'Source Code',
    'app_platforms' => 'App Platforms',
    'archived_task_statuses' => 'Successfully archived :value task statuses',
    'deleted_task_statuses' => 'Successfully deleted :value task statuses',
    'restored_task_statuses' => 'Successfully restored :value task statuses',
    'deleted_expense_categories' => 'Successfully deleted expense :value categories',
    'restored_expense_categories' => 'Successfully restored expense :value categories',
    'archived_recurring_invoices' => 'Successfully archived recurring :value invoices',
    'deleted_recurring_invoices' => 'Successfully deleted recurring :value invoices',
    'restored_recurring_invoices' => 'Successfully restored recurring :value invoices',
    'archived_webhooks' => 'Successfully archived :value webhooks',
    'deleted_webhooks' => 'Successfully deleted :value webhooks',
    'removed_webhooks' => 'Successfully removed :value webhooks',
    'restored_webhooks' => 'Successfully restored :value webhooks',
    'api_docs' => 'API Docs',
    'archived_tokens' => 'Successfully archived :value tokens',
    'deleted_tokens' => 'Successfully deleted :value tokens',
    'restored_tokens' => 'Successfully restored :value tokens',
    'archived_payment_terms' => 'Successfully archived :value payment terms',
    'deleted_payment_terms' => 'Successfully deleted :value payment terms',
    'restored_payment_terms' => 'Successfully restored :value payment terms',
    'archived_designs' => 'Successfully archived :value designs',
    'deleted_designs' => 'Successfully deleted :value designs',
    'restored_designs' => 'Successfully restored :value designs',
    'restored_credits' => 'Successfully restored :value credits',
    'archived_users' => 'Successfully archived :value users',
    'deleted_users' => 'Successfully deleted :value users',
    'removed_users' => 'Successfully removed :value users',
    'restored_users' => 'Successfully restored :value users',
    'archived_tax_rates' => 'Successfully archived :value tax rates',
    'deleted_tax_rates' => 'Successfully deleted :value tax rates',
    'restored_tax_rates' => 'Successfully restored :value tax rates',
    'archived_company_gateways' => 'Successfully archived :value gateways',
    'deleted_company_gateways' => 'Successfully deleted :value gateways',
    'restored_company_gateways' => 'Successfully restored :value gateways',
    'archived_groups' => 'Successfully archived :value groups',
    'deleted_groups' => 'Successfully deleted :value groups',
    'restored_groups' => 'Successfully restored :value groups',
    'archived_documents' => 'Successfully archived :value documents',
    'deleted_documents' => 'Successfully deleted :value documents',
    'restored_documents' => 'Successfully restored :value documents',
    'restored_vendors' => 'Successfully restored :value vendors',
    'restored_expenses' => 'Successfully restored :value expenses',
    'restored_tasks' => 'Successfully restored :value tasks',
    'restored_projects' => 'Successfully restored :value projects',
    'restored_products' => 'Successfully restored :value products',
    'restored_clients' => 'Successfully restored :value clients',
    'restored_invoices' => 'Successfully restored :value invoices',
    'restored_payments' => 'Successfully restored :value payments',
    'restored_quotes' => 'Successfully restored :value quotes',
    'update_app' => 'Update App',
    'started_import' => 'Successfully started import',
    'duplicate_column_mapping' => 'Duplicate column mapping',
    'uses_inclusive_taxes' => 'Uses Inclusive Taxes',
    'is_amount_discount' => 'Is Amount Discount',
    'map_to' => 'Map To',
    'first_row_as_column_names' => 'Use first row as column names',
    'no_file_selected' => 'No File Selected',
    'import_type' => 'Import Type',
    'draft_mode' => 'Draft Mode',
    'draft_mode_help' => 'Preview updates faster but is less accurate',
    'show_product_discount' => 'Show Product Discount',
    'show_product_discount_help' => 'Display a line item discount field',
    'tax_name3' => 'Tax Name 3',
    'debug_mode_is_enabled' => '',
    'debug_mode_is_enabled_help' => '',
    'running_tasks' => 'Running Tasks',
    'recent_tasks' => 'Recent Tasks',
    'recent_expenses' => 'Recent Expenses',
    'upcoming_expenses' => 'Upcoming Expenses',
    'search_payment_term' => 'Search 1 Payment Term',
    'search_payment_terms' => 'Search :count Payment Terms',
    'save_and_preview' => 'Save and Preview',
    'save_and_email' => 'Save and Email',
    'converted_balance' => 'Converted Balance',
    'is_sent' => 'Is Sent',
    'document_upload' => 'Document Upload',
    'document_upload_help' => 'Enable clients to upload documents',
    'expense_total' => 'Expense Total',
    'enter_taxes' => 'Enter Taxes',
    'by_rate' => 'By Rate',
    'by_amount' => 'By Amount',
    'enter_amount' => 'Enter Amount',
    'before_taxes' => 'Before Taxes',
    'after_taxes' => 'After Taxes',
    'color' => 'Color',
    'show' => 'Show',
    'empty_columns' => 'Empty Columns',
    'project_name' => 'Project Name',
    'counter_pattern_error' => 'To use :client_counter please add either :client_number or :client_id_number to prevent conflicts',
    'this_quarter' => 'This Quarter',
    'to_update_run' => 'To update run',
    'registration_url' => 'Registration URL',
    'show_product_cost' => 'Show Product Cost',
    'complete' => 'Complete',
    'next' => 'Next',
    'next_step' => 'Next step',
    'notification_credit_sent_subject' => 'Credit :invoice was sent to :client',
    'notification_credit_viewed_subject' => 'Credit :invoice was viewed by :client',
    'notification_credit_sent' => 'The following client :client was emailed Credit :invoice for :amount.',
    'notification_credit_viewed' => 'The following client :client viewed Credit :credit for :amount.',
    'reset_password_text' => 'Enter your email to reset your password.',
    'password_reset' => 'Password reset',
    'account_login_text' => 'Welcome! Glad to see you.',
    'request_cancellation' => 'Request cancellation',
    'delete_payment_method' => 'Delete Payment Method',
    'about_to_delete_payment_method' => 'You are about to delete the payment method.',
    'action_cant_be_reversed' => 'Action can\'t be reversed',
    'profile_updated_successfully' => 'The profile has been updated successfully.',
    'currency_ethiopian_birr' => 'Ethiopian Birr',
    'client_information_text' => 'Use a permanent address where you can receive mail.',
    'status_id' => 'Invoice Status',
    'email_already_register' => 'This email is already linked to an account',
    'locations' => 'Locations',
    'freq_indefinitely' => 'Indefinitely',
    'cycles_remaining' => 'Cycles remaining',
    'i_understand_delete' => 'I understand, delete',
    'download_files' => 'Download Files',
    'download_timeframe' => 'Use this link to download your files, the link will expire in 1 hour.',
    'new_signup' => 'New Signup',
    'new_signup_text' => 'A new account has been created by :user - :email - from IP address: :ip',
    'notification_payment_paid_subject' => 'Payment was made by :client',
    'notification_partial_payment_paid_subject' => 'Partial payment was made by :client',
    'notification_payment_paid' => 'A payment of :amount was made by client :client towards :invoice',
    'notification_partial_payment_paid' => 'A partial payment of :amount was made by client :client towards :invoice',
    'notification_bot' => 'Notification Bot',
    'invoice_number_placeholder' => 'Invoice # :invoice',
    'entity_number_placeholder' => ':entity # :entity_number',
    'email_link_not_working' => 'If the button above isn\'t working for you, please click on the link',
    'display_log' => 'Display Log',
    'send_fail_logs_to_our_server' => 'Report errors to help improve the app',
    'setup' => 'Setup',
    'quick_overview_statistics' => 'Quick overview & statistics',
    'update_your_personal_info' => 'Update your personal information',
    'name_website_logo' => 'Name, website & logo',
    'make_sure_use_full_link' => 'Make sure you use full link to your site',
    'personal_address' => 'Personal address',
    'enter_your_personal_address' => 'Enter your personal address',
    'enter_your_shipping_address' => 'Enter your shipping address',
    'list_of_invoices' => 'List of invoices',
    'with_selected' => 'With selected',
    'invoice_still_unpaid' => 'This invoice is still not paid. Click the button to complete the payment',
    'list_of_recurring_invoices' => 'List of recurring invoices',
    'details_of_recurring_invoice' => 'Here are some details about recurring invoice',
    'cancellation' => 'Cancellation',
    'about_cancellation' => 'In case you want to stop the recurring invoice, please click to request the cancellation.',
    'cancellation_warning' => 'Warning! You are requesting a cancellation of this service. Your service may be cancelled with no further notification to you.',
    'cancellation_pending' => 'Cancellation pending, we\'ll be in touch!',
    'list_of_payments' => 'List of payments',
    'payment_details' => 'Details of the payment',
    'list_of_payment_invoices' => 'Associate invoices',
    'list_of_payment_methods' => 'List of payment methods',
    'payment_method_details' => 'Details of payment method',
    'permanently_remove_payment_method' => 'Permanently remove this payment method.',
    'warning_action_cannot_be_reversed' => 'Warning! This action can not be reversed!',
    'confirmation' => 'Confirmation',
    'list_of_quotes' => 'Quotes',
    'waiting_for_approval' => 'Waiting for approval',
    'quote_still_not_approved' => 'This quote is still not approved',
    'list_of_credits' => 'Credits',
    'required_extensions' => 'Required extensions',
    'php_version' => 'PHP version',
    'writable_env_file' => 'Writable .env file',
    'env_not_writable' => '.env file is not writable by the current user.',
    'minumum_php_version' => 'Minimum PHP version',
    'satisfy_requirements' => 'Make sure all requirements are satisfied.',
    'oops_issues' => 'Oops, something does not look right!',
    'open_in_new_tab' => 'Open in new tab',
    'complete_your_payment' => 'Complete payment',
    'authorize_for_future_use' => 'Authorize payment method for future use',
    'page' => 'Page',
    'per_page' => 'Per page',
    'of' => 'Of',
    'view_credit' => 'View Credit',
    'to_view_entity_password' => 'To view the :entity you need to enter password.',
    'showing_x_of' => 'Showing :first to :last out of :total results',
    'no_results' => 'No results found.',
    'payment_failed_subject' => 'Payment failed for Client :client',
    'payment_failed_body' => 'A payment made by client :client failed with message :message',
    'register' => 'Register',
    'register_label' => 'Create your account in seconds',
    'password_confirmation' => 'Confirm your password',
    'verification' => 'Verification',
    'complete_your_bank_account_verification' => 'Before using a bank account it must be verified.',
    'checkout_com' => 'Checkout.com',
    'footer_label' => 'Copyright © :year :company.',
    'credit_card_invalid' => 'Provided credit card number is not valid.',
    'month_invalid' => 'Provided month is not valid.',
    'year_invalid' => 'Provided year is not valid.',
    'https_required' => 'HTTPS is required, form will fail',
    'if_you_need_help' => 'If you need help you can post to our',
    'update_password_on_confirm' => 'After updating password, your account will be confirmed.',
    'bank_account_not_linked' => 'To pay with a bank account, first you have to add it as payment method.',
    'application_settings_label' => 'Let\'s store basic information about your Invoice Ninja!',
    'recommended_in_production' => 'Highly recommended in production',
    'enable_only_for_development' => 'Enable only for development',
    'test_pdf' => 'Test PDF',
    'checkout_authorize_label' => 'Checkout.com can be can saved as payment method for future use, once you complete your first transaction. Don\'t forget to check "Store credit card details" during payment process.',
    'sofort_authorize_label' => 'Bank account (SOFORT) can be can saved as payment method for future use, once you complete your first transaction. Don\'t forget to check "Store payment details" during payment process.',
    'node_status' => 'Node status',
    'npm_status' => 'NPM status',
    'node_status_not_found' => 'I could not find Node anywhere. Is it installed?',
    'npm_status_not_found' => 'I could not find NPM anywhere. Is it installed?',
    'locked_invoice' => 'This invoice is locked and unable to be modified',
    'downloads' => 'Downloads',
    'resource' => 'Resource',
    'document_details' => 'Details about the document',
    'hash' => 'Hash',
    'resources' => 'Resources',
    'allowed_file_types' => 'Allowed file types:',
    'common_codes' => 'Common codes and their meanings',
    'payment_error_code_20087' => '20087: Bad Track Data (invalid CVV and/or expiry date)',
    'download_selected' => 'Download selected',
    'to_pay_invoices' => 'To pay invoices, you have to',
    'add_payment_method_first' => 'add payment method',
    'no_items_selected' => 'No items selected.',
    'payment_due' => 'Payment due',
    'account_balance' => 'Account Balance',
    'thanks' => 'Thanks',
    'minimum_required_payment' => 'Minimum required payment is :amount',
    'under_payments_disabled' => 'Company doesn\'t support underpayments.',
    'over_payments_disabled' => 'Company doesn\'t support overpayments.',
    'saved_at' => 'Saved at :time',
    'credit_payment' => 'Credit applied to Invoice :invoice_number',
    'credit_subject' => 'New credit :number from :account',
    'credit_message' => 'To view your credit for :amount, click the link below.',
    'payment_type_Crypto' => 'Cryptocurrency',
    'payment_type_Credit' => 'Credit',
    'store_for_future_use' => 'Store for future use',
    'pay_with_credit' => 'Pay with credit',
    'payment_method_saving_failed' => 'Payment method can\'t be saved for future use.',
    'pay_with' => 'Pay with',
    'n/a' => 'N/A',
    'by_clicking_next_you_accept_terms' => 'By clicking "Next" you accept terms.',
    'not_specified' => 'Not specified',
    'before_proceeding_with_payment_warning' => 'Before proceeding with payment, you have to fill following fields',
    'after_completing_go_back_to_previous_page' => 'After completing, go back to previous page.',
    'pay' => 'Pay',
    'instructions' => 'Instructions',
    'notification_invoice_reminder1_sent_subject' => 'Reminder 1 for Invoice :invoice was sent to :client',
    'notification_invoice_reminder2_sent_subject' => 'Reminder 2 for Invoice :invoice was sent to :client',
    'notification_invoice_reminder3_sent_subject' => 'Reminder 3 for Invoice :invoice was sent to :client',
    'notification_invoice_custom_sent_subject' => 'Custom reminder was sent to :client',
    'notification_invoice_reminder_endless_sent_subject' => 'Endless reminder for Invoice :invoice was sent to :client',
    'assigned_user' => 'Assigned User',
    'setup_steps_notice' => 'To proceed to next step, make sure you test each section.',
    'setup_phantomjs_note' => 'Note about Phantom JS. Read more.',
    'minimum_payment' => 'Minimum Payment',
    'no_action_provided' => 'No action provided. If you believe this is wrong, please contact the support.',
    'no_payable_invoices_selected' => 'No payable invoices selected. Make sure you are not trying to pay draft invoice or invoice with zero balance due.',
    'required_payment_information' => 'Required payment details',
    'required_payment_information_more' => 'To complete a payment we need more details about you.',
    'required_client_info_save_label' => 'We will save this, so you don\'t have to enter it next time.',
    'notification_credit_bounced' => 'We were unable to deliver Credit :invoice to :contact. \n :error',
    'notification_credit_bounced_subject' => 'Unable to deliver Credit :invoice',
    'save_payment_method_details' => 'Save payment method details',
    'new_card' => 'New card',
    'new_bank_account' => 'Add Bank Account',
    'company_limit_reached' => 'Limit of :limit companies per account.',
    'credits_applied_validation' => 'Total credits applied cannot be MORE than total of invoices',
    'credit_number_taken' => 'Credit number already taken',
    'credit_not_found' => 'Credit not found',
    'invoices_dont_match_client' => 'Selected invoices are not from a single client',
    'duplicate_credits_submitted' => 'Duplicate credits submitted.',
    'duplicate_invoices_submitted' => 'Duplicate invoices submitted.',
    'credit_with_no_invoice' => 'You must have an invoice set when using a credit in a payment',
    'client_id_required' => 'Client id is required',
    'expense_number_taken' => 'Expense number already taken',
    'invoice_number_taken' => 'Invoice number already taken',
    'payment_id_required' => 'Payment `id` required.',
    'unable_to_retrieve_payment' => 'Unable to retrieve specified payment',
    'invoice_not_related_to_payment' => 'Invoice id :invoice is not related to this payment',
    'credit_not_related_to_payment' => 'Credit id :credit is not related to this payment',
    'max_refundable_invoice' => 'Attempting to refund more than allowed for invoice id :invoice, maximum refundable amount is :amount',
    'refund_without_invoices' => 'Attempting to refund a payment with invoices attached, please specify valid invoice/s to be refunded.',
    'refund_without_credits' => 'Attempting to refund a payment with credits attached, please specify valid credits/s to be refunded.',
    'max_refundable_credit' => 'Attempting to refund more than allowed for credit :credit, maximum refundable amount is :amount',
    'project_client_do_not_match' => 'Project client does not match entity client',
    'quote_number_taken' => 'Quote number already taken',
    'recurring_invoice_number_taken' => 'Recurring Invoice number :number already taken',
    'user_not_associated_with_account' => 'User not associated with this account',
    'amounts_do_not_balance' => 'Amounts do not balance correctly.',
    'insufficient_applied_amount_remaining' => 'Insufficient applied amount remaining to cover payment.',
    'insufficient_credit_balance' => 'Insufficient balance on credit.',
    'one_or_more_invoices_paid' => 'One or more of these invoices have been paid',
    'invoice_cannot_be_refunded' => 'Invoice id :number cannot be refunded',
    'attempted_refund_failed' => 'Attempting to refund :amount only :refundable_amount available for refund',
    'user_not_associated_with_this_account' => 'This user is unable to be attached to this company. Perhaps they have already registered a user on another account?',
    'migration_completed' => 'Migration completed',
    'migration_completed_description' => 'Your migration has completed, please review your data after logging in.',
    'api_404' => '404 | Nothing to see here!',
    'large_account_update_parameter' => 'Cannot load a large account without a updated_at parameter',
    'no_backup_exists' => 'No backup exists for this activity',
    'company_user_not_found' => 'Company User record not found',
    'no_credits_found' => 'No credits found.',
    'action_unavailable' => 'The requested action :action is not available.',
    'no_documents_found' => 'No Documents Found',
    'no_group_settings_found' => 'No group settings found',
    'access_denied' => 'Insufficient privileges to access/modify this resource',
    'invoice_cannot_be_marked_paid' => 'Invoice cannot be marked as paid',
    'invoice_license_or_environment' => 'Invalid license, or invalid environment :environment',
    'route_not_available' => 'Route not available',
    'invalid_design_object' => 'Invalid custom design object',
    'quote_not_found' => 'Quote/s not found',
    'quote_unapprovable' => 'Unable to approve this quote as it has expired.',
    'scheduler_has_run' => 'Scheduler has run',
    'scheduler_has_never_run' => 'Scheduler has never run',
    'self_update_not_available' => 'Self update not available on this system.',
    'user_detached' => 'User detached from company',
    'create_webhook_failure' => 'Failed to create Webhook',
    'payment_message_extended' => 'Thank you for your payment of :amount for :invoice',
    'online_payments_minimum_note' => 'Note: Online payments are supported only if amount is larger than $1 or currency equivalent.',
    'payment_token_not_found' => 'Payment token not found, please try again. If an issue still persist, try with another payment method',
    'vendor_address1' => 'Vendor Street',
    'vendor_address2' => 'Vendor Apt/Suite',
    'partially_unapplied' => 'Partially Unapplied',
    'select_a_gmail_user' => 'Please select a user authenticated with Gmail',
    'list_long_press' => 'List Long Press',
    'show_actions' => 'Show Actions',
    'start_multiselect' => 'Start Multiselect',
    'email_sent_to_confirm_email' => 'An email has been sent to confirm the email address',
    'converted_paid_to_date' => 'Converted Paid to Date',
    'converted_credit_balance' => 'Converted Credit Balance',
    'converted_total' => 'Converted Total',
    'reply_to_name' => 'Reply-To Name',
    'payment_status_-2' => 'Partially Unapplied',
    'color_theme' => 'Color Theme',
    'start_migration' => 'Start Migration',
    'recurring_cancellation_request' => 'Request for recurring invoice cancellation from :contact',
    'recurring_cancellation_request_body' => ':contact from Client :client requested to cancel Recurring Invoice :invoice',
    'hello' => 'Hello',
    'group_documents' => 'Group documents',
    'quote_approval_confirmation_label' => 'Are you sure you want to approve this quote?',
    'migration_select_company_label' => 'Select companies to migrate',
    'force_migration' => 'Force migration',
    'require_password_with_social_login' => 'Require Password with Social Login',
    'stay_logged_in' => 'Stay Logged In',
    'session_about_to_expire' => 'Warning: Your session is about to expire',
    'count_hours' => ':count Hours',
    'count_day' => '1 Day',
    'count_days' => ':count Days',
    'web_session_timeout' => 'Web Session Timeout',
    'security_settings' => 'Security Settings',
    'resend_email' => 'Resend Email',
    'confirm_your_email_address' => 'Please confirm your email address',
    'freshbooks' => 'FreshBooks',
    'invoice2go' => 'Invoice2go',
    'invoicely' => 'Invoicely',
    'waveaccounting' => 'Wave Accounting',
    'zoho' => 'Zoho',
    'accounting' => 'Accounting',
    'required_files_missing' => 'Please provide all CSVs.',
    'migration_auth_label' => 'Let\'s continue by authenticating.',
    'api_secret' => 'API secret',
    'migration_api_secret_notice' => 'You can find API_SECRET in the .env file. If property is missing, leave field blank.',
    'billing_coupon_notice' => 'Your discount will be applied on the checkout.',
    'use_last_email' => 'Use last email',
    'activate_company' => 'Activate Company',
    'activate_company_help' => 'Enable emails, recurring invoices and notifications',
    'an_error_occurred_try_again' => 'An error occurred, please try again',
    'please_first_set_a_password' => 'Please first set a password',
    'changing_phone_disables_two_factor' => 'Warning: Changing your phone number will disable 2FA',
    'help_translate' => 'Help Translate',
    'please_select_a_country' => 'Please select a country',
    'disabled_two_factor' => 'Successfully disabled 2FA',
    'connected_google' => 'Successfully connected account',
    'disconnected_google' => 'Successfully disconnected account',
    'delivered' => 'Delivered',
    'spam' => 'Spam',
    'view_docs' => 'View Docs',
    'enter_phone_to_enable_two_factor' => 'Please provide a mobile phone number to enable two factor authentication',
    'send_sms' => 'Send SMS',
    'sms_code' => 'SMS Code',
    'connect_google' => 'Connect Google',
    'disconnect_google' => 'Disconnect Google',
    'disable_two_factor' => 'Disable Two Factor',
    'invoice_task_datelog' => 'Invoice Task Datelog',
    'invoice_task_datelog_help' => 'Add date details to the invoice line items',
    'promo_code' => 'Promo code',
    'recurring_invoice_issued_to' => 'Recurring invoice issued to',
    'subscription' => 'Subscription',
    'new_subscription' => 'New Subscription',
    'deleted_subscription' => 'Successfully deleted subscription',
    'removed_subscription' => 'Successfully removed subscription',
    'restored_subscription' => 'Successfully restored subscription',
    'search_subscription' => 'Search 1 Subscription',
    'search_subscriptions' => 'Search :count Subscriptions',
    'subdomain_is_not_available' => 'Subdomain is not available',
    'connect_gmail' => 'Connect Gmail',
    'disconnect_gmail' => 'Disconnect Gmail',
    'connected_gmail' => 'Successfully connected Gmail',
    'disconnected_gmail' => 'Successfully disconnected Gmail',
    'update_fail_help' => 'Changes to the codebase may be blocking the update, you can run this command to discard the changes:',
    'client_id_number' => 'Client ID Number',
    'count_minutes' => ':count Minutes',
    'password_timeout' => 'Password Timeout',
    'shared_invoice_credit_counter' => 'Share Invoice/Credit Counter',
    'activity_80' => ':user created subscription :subscription',
    'activity_81' => ':user updated subscription :subscription',
    'activity_82' => ':user archived subscription :subscription',
    'activity_83' => ':user deleted subscription :subscription',
    'activity_84' => ':user restored subscription :subscription',
    'amount_greater_than_balance_v5' => 'The amount is greater than the invoice balance. You cannot overpay an invoice.',
    'click_to_continue' => 'Click to continue',
    'notification_invoice_created_body' => 'The following invoice :invoice was created for client :client for :amount.',
    'notification_invoice_created_subject' => 'Invoice :invoice was created for :client',
    'notification_quote_created_body' => 'The following quote :invoice was created for client :client for :amount.',
    'notification_quote_created_subject' => 'Quote :invoice was created for :client',
    'notification_credit_created_body' => 'The following credit :invoice was created for client :client for :amount.',
    'notification_credit_created_subject' => 'Credit :invoice was created for :client',
    'max_companies' => 'Maximum companies migrated',
    'max_companies_desc' => 'You have reached your maximum number of companies. Delete existing companies to migrate new ones.',
    'migration_already_completed' => 'Company already migrated',
    'migration_already_completed_desc' => 'Looks like you already migrated <b> :company_name </b>to the V5 version of the Invoice Ninja. In case you want to start over, you can force migrate to wipe existing data.',
    'payment_method_cannot_be_authorized_first' => 'This payment method can be can saved for future use, once you complete your first transaction. Don\'t forget to check "Store details" during payment process.',
    'new_account' => 'New account',
    'activity_100' => ':user created recurring invoice :recurring_invoice',
    'activity_101' => ':user updated recurring invoice :recurring_invoice',
    'activity_102' => ':user archived recurring invoice :recurring_invoice',
    'activity_103' => ':user deleted recurring invoice :recurring_invoice',
    'activity_104' => ':user restored recurring invoice :recurring_invoice',
    'new_login_detected' => 'New login detected for your account.',
    'new_login_description' => 'You recently logged in to your Invoice Ninja account from a new location or device:<br><br><b>IP:</b> :ip<br><b>Time:</b> :time<br><b>Email:</b> :email',
    'contact_details' => 'Contact Details',
    'download_backup_subject' => 'Your company backup is ready for download',
    'account_passwordless_login' => 'Account passwordless login',
    'user_duplicate_error' => 'Cannot add the same user to the same company',
    'user_cross_linked_error' => 'User exists but cannot be crossed linked to multiple accounts',
    'ach_verification_notification_label' => 'ACH verification',
    'ach_verification_notification' => 'Connecting bank accounts require verification. Payment gateway will automatically send two small deposits for this purpose. These deposits take 1-2 business days to appear on the customer\'s online statement.',
    'login_link_requested_label' => 'Login link requested',
    'login_link_requested' => 'There was a request to login using link. If you did not request this, it\'s safe to ignore it.',
    'invoices_backup_subject' => 'Your invoices are ready for download',
    'migration_failed_label' => 'Migration failed',
    'migration_failed' => 'Looks like something went wrong with the migration for the following company:',
    'client_email_company_contact_label' => 'If you have any questions please contact us, we\'re here to help!',
    'quote_was_approved_label' => 'Quote was approved',
    'quote_was_approved' => 'We would like to inform you that quote was approved.',
    'company_import_failure_subject' => 'Error importing :company',
    'company_import_failure_body' => 'There was an error importing the company data, the error message was:',
    'recurring_invoice_due_date' => 'Due Date',
    'amount_cents' => 'Amount in pennies,pence or cents. ie for $0.10 please enter 10',
    'default_payment_method_label' => 'Default Payment Method',
    'default_payment_method' => 'Make this your preferred way of paying.',
    'already_default_payment_method' => 'This is your preferred way of paying.',
    'auto_bill_disabled' => 'Auto Bill Disabled',
    'select_payment_method' => 'Select a payment method:',
    'login_without_password' => 'Log in without password',
    'email_sent' => 'Email me when an invoice is <b>sent</b>',
    'one_time_purchases' => 'One time purchases',
    'recurring_purchases' => 'Recurring purchases',
    'you_might_be_interested_in_following' => 'You might be interested in the following',
    'quotes_with_status_sent_can_be_approved' => 'Only quotes with "Sent" status can be approved. Expired quotes cannot be approved.',
    'no_quotes_available_for_download' => 'No quotes available for download.',
    'copyright' => 'Copyright',
    'user_created_user' => ':user created :created_user at :time',
    'company_deleted' => 'Company deleted',
    'company_deleted_body' => 'Company [ :company ] was deleted by :user',
    'back_to' => 'Back to :url',
    'stripe_connect_migration_title' => 'Connect your Stripe Account',
    'stripe_connect_migration_desc' => 'Invoice Ninja v5 uses Stripe Connect to link your Stripe account to Invoice Ninja. This provides an additional layer of security for your account. Now that you data has migrated, you will need to Authorize Stripe to accept payments in v5.<br><br>To do this, navigate to Settings > Online Payments > Configure Gateways. Click on Stripe Connect and then under Settings click Setup Gateway. This will take you to Stripe to authorize Invoice Ninja and on your return your account will be successfully linked!',
    'email_quota_exceeded_subject' => 'Account email quota exceeded.',
    'email_quota_exceeded_body' => 'In a 24 hour period you have sent :quota emails. <br> We have paused your outbound emails.<br><br> Your email quota will reset at 23:00 UTC.',
    'auto_bill_option' => 'Opt in or out of having this invoice automatically charged.',
    'lang_Arabic' => 'Arabic',
    'lang_Persian' => 'Persian',
    'lang_Latvian' => 'Latvian',
    'expiry_date' => 'Expiry date',
    'cardholder_name' => 'Card holder name',
    'recurring_quote_number_taken' => 'Recurring Quote number :number already taken',
    'account_type' => 'Account type',
    'locality' => 'Locality',
    'checking' => 'Checking',
    'savings' => 'Savings',
    'unable_to_verify_payment_method' => 'Unable to verify payment method.',
    'generic_gateway_error' => 'Gateway configuration error. Please check your credentials.',
    'my_documents' => 'My documents',
    'payment_method_cannot_be_preauthorized' => 'This payment method cannot be preauthorized.',
    'kbc_cbc' => 'KBC/CBC',
    'bancontact' => 'Bancontact',
    'sepa_mandat' => 'By providing your IBAN and confirming this payment, you are authorizing :company and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with those instructions. You are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited.',
    'ideal' => 'iDEAL',
    'bank_account_holder' => 'Bank Account Holder',
    'aio_checkout' => 'All-in-one checkout',
    'przelewy24' => 'Przelewy24',
    'przelewy24_accept' => 'I declare that I have familiarized myself with the regulations and information obligation of the Przelewy24 service.',
    'giropay' => 'GiroPay',
    'giropay_law' => 'By entering your Customer information (such as name, sort code and account number) you (the Customer) agree that this information is given voluntarily.',
    'klarna' => 'Klarna',
    'eps' => 'EPS',
    'becs' => 'BECS Direct Debit',
    'bacs' => 'BACS Direct Debit',
    'payment_type_BACS' => 'BACS Direct Debit',
    'missing_payment_method' => 'Please add a payment method first, before trying to pay.',
    'becs_mandate' => 'By providing your bank account details, you agree to this <a class="underline" href="https://stripe.com/au-becs-dd-service-agreement/legal">Direct Debit Request and the Direct Debit Request service agreement</a>, and authorise Stripe Payments Australia Pty Ltd ACN *********** Direct Debit User ID number 507156 (“Stripe”) to debit your account through the Bulk Electronic Clearing System (BECS) on behalf of :company (the “Merchant”) for any amounts separately communicated to you by the Merchant. You certify that you are either an account holder or an authorised signatory on the account listed above.',
    'you_need_to_accept_the_terms_before_proceeding' => 'You need to accept the terms before proceeding.',
    'direct_debit' => 'Direct Debit',
    'clone_to_expense' => 'Clone to Expense',
    'checkout' => 'Checkout',
    'acss' => 'ACSS Debit',
    'invalid_amount' => 'Invalid amount. Number/Decimal values only.',
    'client_payment_failure_body' => 'Payment for Invoice :invoice for amount :amount failed.',
    'browser_pay' => 'Google Pay, Apple Pay, Microsoft Pay',
    'no_available_methods' => 'We can\'t find any credit cards on your device. <a href="https://invoiceninja.github.io/docs/payments#apple-pay-google-pay-microsoft-pay" target="_blank" class="underline">Read more about this.</a>',
    'gocardless_mandate_not_ready' => 'Payment mandate is not ready. Please try again later.',
    'payment_type_instant_bank_pay' => 'Instant Bank Pay',
    'payment_type_iDEAL' => 'iDEAL',
    'payment_type_Przelewy24' => 'Przelewy24',
    'payment_type_Mollie Bank Transfer' => 'Mollie Bank Transfer',
    'payment_type_KBC/CBC' => 'KBC/CBC',
    'payment_type_Instant Bank Pay' => 'Instant Bank Pay',
    'payment_type_Hosted Page' => 'Hosted Page',
    'payment_type_GiroPay' => 'GiroPay',
    'payment_type_EPS' => 'EPS',
    'payment_type_Direct Debit' => 'Direct Debit',
    'payment_type_Bancontact' => 'Bancontact',
    'payment_type_BECS' => 'BECS',
    'payment_type_ACSS' => 'ACSS',
    'gross_line_total' => 'Gross Line Total',
    'lang_Slovak' => 'Slovak',
    'normal' => 'Normal',
    'large' => 'Large',
    'extra_large' => 'Extra Large',
    'show_pdf_preview' => 'Show PDF Preview',
    'show_pdf_preview_help' => 'Display PDF preview while editing invoices',
    'print_pdf' => 'Print PDF',
    'remind_me' => 'Remind Me',
    'instant_bank_pay' => 'Instant Bank Pay',
    'click_selected' => 'Click Selected',
    'hide_preview' => 'Hide Preview',
    'edit_record' => 'Edit Record',
    'credit_is_more_than_invoice' => 'The credit amount can not be more than the invoice amount',
    'please_set_a_password' => 'Please set an account password',
    'recommend_desktop' => 'We recommend using the desktop app for the best performance',
    'recommend_mobile' => 'We recommend using the mobile app for the best performance',
    'disconnected_gateway' => 'Successfully disconnected gateway',
    'disconnect' => 'Disconnect',
    'add_to_invoices' => 'Add to Invoices',
    'bulk_download' => 'Download',
    'persist_data_help' => 'Save data locally to enable the app to start faster, disabling may improve performance in large accounts',
    'persist_ui' => 'Persist UI',
    'persist_ui_help' => 'Save UI state locally to enable the app to start at the last location, disabling may improve performance',
    'client_postal_code' => 'Client Postal Code',
    'client_vat_number' => 'Client VAT Number',
    'has_tasks' => 'Has Tasks',
    'registration' => 'Registration',
    'unauthorized_stripe_warning' => 'Please authorize Stripe to accept online payments.',
    'update_all_records' => 'Update all records',
    'set_default_company' => 'Set Default Company',
    'updated_company' => 'Successfully updated company',
    'kbc' => 'KBC',
    'why_are_you_leaving' => 'Help us improve by telling us why (optional)',
    'webhook_success' => 'Webhook Success',
    'error_cross_client_tasks' => 'Tasks must all belong to the same client',
    'error_cross_client_expenses' => 'Expenses must all belong to the same client',
    'app' => 'App',
    'for_best_performance' => 'For the best performance download the :app app',
    'bulk_email_invoice' => 'Email Invoice',
    'bulk_email_quote' => 'Email Quote',
    'bulk_email_credit' => 'Email Credit',
    'removed_recurring_expense' => 'Successfully removed recurring expense',
    'search_recurring_expense' => 'Search Recurring Expense',
    'search_recurring_expenses' => 'Search Recurring Expenses',
    'last_sent_date' => 'Last Sent Date',
    'include_drafts' => 'Include Drafts',
    'include_drafts_help' => 'Include draft records in reports',
    'is_invoiced' => 'Is Invoiced',
    'change_plan' => 'Manage Plan',
    'persist_data' => 'Persist Data',
    'customer_count' => 'Customer Count',
    'verify_customers' => 'Verify Customers',
    'google_analytics_tracking_id' => 'Google Analytics Tracking ID',
    'decimal_comma' => 'Decimal Comma',
    'use_comma_as_decimal_place' => 'Use comma as decimal place in forms',
    'select_method' => 'Select Method',
    'select_platform' => 'Select Platform',
    'use_web_app_to_connect_gmail' => 'Please use the web app to connect to Gmail',
    'expense_tax_help' => 'Item tax rates are disabled',
    'enable_markdown' => 'Enable Markdown',
    'enable_markdown_help' => 'Convert markdown to HTML on the PDF',
    'add_second_contact' => 'Add Second Contact',
    'previous_page' => 'Previous Page',
    'next_page' => 'Next Page',
    'export_colors' => 'Export Colors',
    'import_colors' => 'Import Colors',
    'clear_all' => 'Clear All',
    'contrast' => 'Contrast',
    'custom_colors' => 'Custom Colors',
    'colors' => 'Colors',
    'sidebar_active_background_color' => 'Sidebar Active Background Color',
    'sidebar_active_font_color' => 'Sidebar Active Font Color',
    'sidebar_inactive_background_color' => 'Sidebar Inactive Background Color',
    'sidebar_inactive_font_color' => 'Sidebar Inactive Font Color',
    'table_alternate_row_background_color' => 'Table Alternate Row Background Color',
    'invoice_header_background_color' => 'Invoice Header Background Color',
    'invoice_header_font_color' => 'Invoice Header Font Color',
    'review_app' => 'Review App',
    'check_status' => 'Check Status',
    'free_trial' => 'Free Trial',
    'free_trial_help' => 'All accounts receive a two week trial of the Pro plan, once the trial ends your account will automatically change to the free plan.',
    'free_trial_ends_in_days' => 'The Pro plan trial ends in :count days, click to upgrade.',
    'free_trial_ends_today' => 'Today is the last day of the Pro plan trial, click to upgrade.',
    'change_email' => 'Change Email',
    'client_portal_domain_hint' => 'Optionally configure a separate client portal domain',
    'tasks_shown_in_portal' => 'Tasks Shown in Portal',
    'uninvoiced' => 'Uninvoiced',
    'subdomain_guide' => 'The subdomain is used in the client portal to personalize links to match your brand. ie, https://your-brand.invoicing.co',
    'send_time' => 'Send Time',
    'import_settings' => 'Import Settings',
    'json_file_missing' => 'Please provide the JSON file',
    'json_option_missing' => 'Please select to import the settings and/or data',
    'json' => 'JSON',
    'no_payment_types_enabled' => 'No payment types enabled',
    'wait_for_data' => 'Please wait for the data to finish loading',
    'net_total' => 'Net Total',
    'has_taxes' => 'Has Taxes',
    'import_customers' => 'Import Customers',
    'imported_customers' => 'Successfully started importing customers',
    'login_success' => 'Successful Login',
    'login_failure' => 'Failed Login',
    'exported_data' => 'Once the file is ready you\'ll receive an email with a download link',
    'include_deleted_clients' => 'Include Deleted Clients',
    'include_deleted_clients_help' => 'Load records belonging to deleted clients',
    'step_1_sign_in' => 'Step 1: Sign In',
    'step_2_authorize' => 'Step 2: Authorize',
    'account_id' => 'Account ID',
    'migration_not_yet_completed' => 'The migration has not yet completed',
    'show_task_end_date' => 'Show Task End Date',
    'show_task_end_date_help' => 'Enable specifying the task end date',
    'gateway_setup' => 'Gateway Setup',
    'preview_sidebar' => 'Preview Sidebar',
    'years_data_shown' => 'Years Data Shown',
    'ended_all_sessions' => 'Successfully ended all sessions',
    'end_all_sessions' => 'End All Sessions',
    'count_session' => '1 Session',
    'count_sessions' => ':count Sessions',
    'invoice_created' => 'Invoice Created',
    'quote_created' => 'Quote Created',
    'credit_created' => 'Credit Created',
    'enterprise' => 'Enterprise',
    'invoice_item' => 'Invoice Item',
    'quote_item' => 'Quote Item',
    'order' => 'Order',
    'search_kanban' => 'Search Kanban',
    'search_kanbans' => 'Search Kanban',
    'move_top' => 'Move Top',
    'move_up' => 'Move Up',
    'move_down' => 'Move Down',
    'move_bottom' => 'Move Bottom',
    'body_variable_missing' => 'Error: the custom email must include a :body variable',
    'add_body_variable_message' => 'Make sure to include a :body variable',
    'view_date_formats' => 'View Date Formats',
    'is_viewed' => 'Is Viewed',
    'letter' => 'Letter',
    'legal' => 'Legal',
    'page_layout' => 'Page Layout',
    'portrait' => 'Portrait',
    'landscape' => 'Landscape',
    'owner_upgrade_to_paid_plan' => 'The account owner can upgrade to a paid plan to enable the advanced advanced settings',
    'upgrade_to_paid_plan' => 'Upgrade to a paid plan to enable the advanced settings',
    'invoice_payment_terms' => 'Invoice Payment Terms',
    'quote_valid_until' => 'Quote Valid Until',
    'no_headers' => 'No Headers',
    'add_header' => 'Add Header',
    'remove_header' => 'Remove Header',
    'return_url' => 'Return URL',
    'rest_method' => 'REST Method',
    'header_key' => 'Header Key',
    'header_value' => 'Header Value',
    'recurring_products' => 'Recurring Products',
    'promo_discount' => 'Promo Discount',
    'allow_cancellation' => 'Allow Cancellation',
    'per_seat_enabled' => 'Per Seat Enabled',
    'max_seats_limit' => 'Max Seats Limit',
    'trial_enabled' => 'Trial Enabled',
    'trial_duration' => 'Trial Duration',
    'allow_query_overrides' => 'Allow Query Overrides',
    'allow_plan_changes' => 'Allow Plan Changes',
    'plan_map' => 'Plan Map',
    'refund_period' => 'Refund Period',
    'webhook_configuration' => 'Webhook Configuration',
    'purchase_page' => 'Purchase Page',
    'email_bounced' => 'Email Bounced',
    'email_spam_complaint' => 'Spam Complaint',
    'email_delivery' => 'Email Delivery',
    'webhook_response' => 'Webhook Response',
    'pdf_response' => 'PDF Response',
    'authentication_failure' => 'Authentication Failure',
    'pdf_failed' => 'PDF Failed',
    'pdf_success' => 'PDF Success',
    'modified' => 'Modified',
    'html_mode' => 'HTML Mode',
    'html_mode_help' => 'Preview updates faster but is less accurate',
    'status_color_theme' => 'Status Color Theme',
    'load_color_theme' => 'Load Color Theme',
    'lang_Estonian' => 'Estonian',
    'marked_credit_as_paid' => 'Successfully marked credit as paid',
    'marked_credits_as_paid' => 'Successfully marked credits as paid',
    'wait_for_loading' => 'Data loading - please wait for it to complete',
    'wait_for_saving' => 'Data saving - please wait for it to complete',
    'html_preview_warning' => 'Note: changes made here are only previewed, they must be applied in the tabs above to be saved',
    'remaining' => 'Remaining',
    'invoice_paid' => 'Invoice Paid',
    'activity_120' => ':user created recurring expense :recurring_expense',
    'activity_121' => ':user updated recurring expense :recurring_expense',
    'activity_122' => ':user archived recurring expense :recurring_expense',
    'activity_123' => ':user deleted recurring expense :recurring_expense',
    'activity_124' => ':user restored recurring expense :recurring_expense',
    'fpx' => "FPX",
    'to_view_entity_set_password' => 'To view the :entity you need to set a password.',
    'unsubscribe' => 'Unsubscribe',
    'unsubscribed' => 'Unsubscribed',
    'unsubscribed_text' => 'You have been removed from notifications for this document',
    'client_shipping_state' => 'Client Shipping State',
    'client_shipping_city' => 'Client Shipping City',
    'client_shipping_postal_code' => 'Client Shipping Postal Code',
    'client_shipping_country' => 'Client Shipping Country',
    'load_pdf' => 'Load PDF',
    'start_free_trial' => 'Start Free Trial',
    'start_free_trial_message' => 'Start your FREE 14 day trial of the Pro Plan',
    'due_on_receipt' => 'Due on Receipt',
    'is_paid' => 'Is Paid',
    'age_group_paid' => 'Paid',
    'id' => 'Id',
    'convert_to' => 'Convert To',
    'client_currency' => 'Client Currency',
    'company_currency' => 'Company Currency',
    'custom_emails_disabled_help' => 'To prevent spam we require upgrading to a paid account to customize the email',
    'upgrade_to_add_company' => 'Upgrade your plan to add companies',
    'file_saved_in_downloads_folder' => 'The file has been saved in the downloads folder',
    'small' => 'Small',
    'quotes_backup_subject' => 'Your quotes are ready for download',
    'credits_backup_subject' => 'Your credits are ready for download',
    'document_download_subject' => 'Your documents are ready for download',
    'reminder_message' => 'Reminder for invoice :number for :balance',
    'gmail_credentials_invalid_subject' => 'Send with GMail invalid credentials',
    'gmail_credentials_invalid_body' => 'Your GMail credentials are not correct, please log into the administrator portal and navigate to Settings > User Details and disconnect and reconnect your GMail account. We will send you this notification daily until this issue is resolved',
    'total_columns' => 'Totals Fields',
    'view_task' => 'View Task',
    'cancel_invoice' => 'Cancel',
    'changed_status' => 'Successfully changed task status',
    'change_status' => 'Change Status',
    'enable_touch_events' => 'Enable Touch Events',
    'enable_touch_events_help' => 'Support drag events to scroll',
    'after_saving' => 'After Saving',
    'view_record' => 'View Record',
    'enable_email_markdown' => 'Enable Email Markdown',
    'enable_email_markdown_help' => 'Use visual markdown editor for emails',
    'enable_pdf_markdown' => 'Enable PDF Markdown',
    'json_help' => 'Note: JSON files generated by the v4 app are not supported',
    'release_notes' => 'Release Notes',
    'upgrade_to_view_reports' => 'Upgrade your plan to view reports',
    'started_tasks' => 'Successfully started :value tasks',
    'stopped_tasks' => 'Successfully stopped :value tasks',
    'approved_quote' => 'Successfully apporved quote',
    'approved_quotes' => 'Successfully :value approved quotes',
    'client_website' => 'Client Website',
    'invalid_time' => 'Invalid Time',
    'signed_in_as' => 'Signed in as',
    'total_results' => 'Total results',
    'restore_company_gateway' => 'Restore gateway',
    'archive_company_gateway' => 'Archive gateway',
    'delete_company_gateway' => 'Delete gateway',
    'exchange_currency' => 'Exchange currency',
    'tax_amount1' => 'Tax Amount 1',
    'tax_amount2' => 'Tax Amount 2',
    'tax_amount3' => 'Tax Amount 3',
    'update_project' => 'Update Project',
    'auto_archive_invoice_cancelled' => 'Auto Archive Cancelled Invoice',
    'auto_archive_invoice_cancelled_help' => 'Automatically archive invoices when cancelled',
    'no_invoices_found' => 'No invoices found',
    'created_record' => 'Successfully created record',
    'auto_archive_paid_invoices' => 'Auto Archive Paid',
    'auto_archive_paid_invoices_help' => 'Automatically archive invoices when they are paid.',
    'auto_archive_cancelled_invoices' => 'Auto Archive Cancelled',
    'auto_archive_cancelled_invoices_help' => 'Automatically archive invoices when cancelled.',
    'alternate_pdf_viewer' => 'Alternate PDF Viewer',
    'alternate_pdf_viewer_help' => 'Improve scrolling over the PDF preview [BETA]',
    'currency_cayman_island_dollar' => 'Cayman Island Dollar',
    'download_report_description' => 'Please see attached file to check your report.',
    'left' => 'Left',
    'right' => 'Right',
    'center' => 'Center',
    'page_numbering' => 'Page Numbering',
    'page_numbering_alignment' => 'Page Numbering Alignment',
    'invoice_sent_notification_label' => 'Invoice Sent',
    'show_product_description' => 'Show Product Description',
    'show_product_description_help' => 'Include the description in the product dropdown',
    'invoice_items' => 'Invoice Items',
    'quote_items' => 'Quote Items',
    'profitloss' => 'Profit and Loss',
    'import_format' => 'Import Format',
    'export_format' => 'Export Format',
    'export_type' => 'Export Type',
    'stop_on_unpaid' => 'Stop On Unpaid',
    'stop_on_unpaid_help' => 'Stop creating recurring invoices if the last invoice is unpaid.',
    'use_quote_terms' => 'Use Quote Terms',
    'use_quote_terms_help' => 'When converting a quote to an invoice',
    'add_country' => 'Add Country',
    'enable_tooltips' => 'Enable Tooltips',
    'enable_tooltips_help' => 'Show tooltips when hovering the mouse',
    'multiple_client_error' => 'Error: records belong to more than one client',
    'login_label' => 'Login to an existing account',
    'purchase_order' => 'Purchase Order',
    'purchase_order_number' => 'Purchase Order Number',
    'purchase_order_number_short' => 'Purchase Order #',
    'inventory_notification_subject' => 'Inventory threshold notification for product: :product',
    'inventory_notification_body' => 'Threshold of :amount has been reached for product: :product',
    'activity_130' => ':user created purchase order :purchase_order',
    'activity_131' => ':user updated purchase order :purchase_order',
    'activity_132' => ':user archived purchase order :purchase_order',
    'activity_133' => ':user deleted purchase order :purchase_order',
    'activity_134' => ':user restored purchase order :purchase_order',
    'activity_135' => ':user emailed purchase order :purchase_order',
    'activity_136' => ':contact viewed purchase order :purchase_order',
    'purchase_order_subject' => 'New Purchase Order :number from :account',
    'purchase_order_message' => 'To view your purchase order for :amount, click the link below.',
    'view_purchase_order' => 'View Purchase Order',
    'purchase_orders_backup_subject' => 'Your purchase orders are ready for download',
    'notification_purchase_order_viewed_subject' => 'Purchase Order :invoice was viewed by :client',
    'notification_purchase_order_viewed' => 'The following vendor :client viewed Purchase Order :invoice for :amount.',
    'purchase_order_date' => 'Purchase Order Date',
    'purchase_orders' => 'Purchase Orders',
    'purchase_order_number_placeholder' => 'Purchase Order # :purchase_order',
    'accepted' => 'Accepted',
    'activity_137' => ':contact accepted purchase order :purchase_order',
    'vendor_information' => 'Vendor Information',
    'notification_purchase_order_accepted_subject' => 'Purchase Order :purchase_order was accepted by :vendor',
    'notification_purchase_order_accepted' => 'The following vendor :vendor accepted Purchase Order :purchase_order for :amount.',
    'amount_received' => 'Amount received',
    'purchase_order_already_expensed' => 'Already converted to an expense.',
    'convert_to_expense' => 'Convert to Expense',
    'add_to_inventory' => 'Add to Inventory',
    'added_purchase_order_to_inventory' => 'Successfully added purchase order to inventory',
    'added_purchase_orders_to_inventory' => 'Successfully added purchase orders to inventory',
    'client_document_upload' => 'Client Document Upload',
    'vendor_document_upload' => 'Vendor Document Upload',
    'vendor_document_upload_help' => 'Enable vendors to upload documents',
    'are_you_enjoying_the_app' => 'Are you enjoying the app?',
    'yes_its_great' => 'Yes, it\'s great!',
    'not_so_much' => 'Not so much',
    'would_you_rate_it' => 'Great to hear! Would you like to rate it?',
    'would_you_tell_us_more' => 'Sorry to hear it! Would you like to tell us more?',
    'sure_happy_to' => 'Sure, happy to',
    'no_not_now' => 'No, not now',
    'add' => 'Add',
    'last_sent_template' => 'Last Sent Template',
    'enable_flexible_search' => 'Enable Flexible Search',
    'enable_flexible_search_help' => 'Match non-contiguous characters, ie. "ct" matches "cat"',
    'vendor_details' => 'Vendor Details',
    'purchase_order_details' => 'Purchase Order Details',
    'qr_iban' => 'QR IBAN',
    'besr_id' => 'BESR ID',
    'clone_to_purchase_order' => 'Clone to PO',
    'vendor_email_not_set' => 'Vendor does not have an email address set',
    'bulk_send_email' => 'Send Email',
    'marked_purchase_order_as_sent' => 'Successfully marked purchase order as sent',
    'marked_purchase_orders_as_sent' => 'Successfully marked purchase orders as sent',
    'accepted_purchase_order' => 'Successfully accepted purchase order',
    'accepted_purchase_orders' => 'Successfully accepted purchase orders',
    'cancelled_purchase_order' => 'Successfully cancelled purchase order',
    'cancelled_purchase_orders' => 'Successfully cancelled purchase orders',
    'please_select_a_vendor' => 'Please select a vendor',
    'purchase_order_total' => 'Purchase Order Total',
    'email_purchase_order' => 'Email Purchase Order',
    'bulk_email_purchase_order' => 'Email Purchase Order',
    'disconnected_email' => 'Successfully disconnected email',
    'connect_email' => 'Connect Email',
    'disconnect_email' => 'Disconnect Email',
    'use_web_app_to_connect_microsoft' => 'Please use the web app to connect to Microsoft',
    'email_provider' => 'Email Provider',
    'connect_microsoft' => 'Connect Microsoft',
    'disconnect_microsoft' => 'Disconnect Microsoft',
    'connected_microsoft' => 'Successfully connected Microsoft',
    'disconnected_microsoft' => 'Successfully disconnected Microsoft',
    'microsoft_sign_in' => 'Login with Microsoft',
    'microsoft_sign_up' => 'Sign up with Microsoft',
    'emailed_purchase_order' => 'Successfully queued purchase order to be sent',
    'emailed_purchase_orders' => 'Successfully queued purchase orders to be sent',
    'enable_react_app' => 'Change to the React web app',
    'purchase_order_design' => 'Purchase Order Design',
    'purchase_order_terms' => 'Purchase Order Terms',
    'purchase_order_footer' => 'Purchase Order Footer',
    'require_purchase_order_signature' => 'Purchase Order Signature',
    'require_purchase_order_signature_help' => 'Require vendor to provide their signature.',
    'new_purchase_order' => 'New Purchase Order',
    'edit_purchase_order' => 'Edit Purchase Order',
    'created_purchase_order' => 'Successfully created purchase order',
    'updated_purchase_order' => 'Successfully updated purchase order',
    'archived_purchase_order' => 'Successfully archived purchase order',
    'deleted_purchase_order' => 'Successfully deleted purchase order',
    'removed_purchase_order' => 'Successfully removed purchase order',
    'restored_purchase_order' => 'Successfully restored purchase order',
    'search_purchase_order' => 'Search Purchase Order',
    'search_purchase_orders' => 'Search Purchase Orders',
    'login_url' => 'Login URL',
    'enable_applying_payments' => 'Manual Overpayments',
    'enable_applying_payments_help' => 'Support adding an overpayment amount manually on a payment',
    'stock_quantity' => 'Stock Quantity',
    'notification_threshold' => 'Notification Threshold',
    'track_inventory' => 'Track Inventory',
    'track_inventory_help' => 'Display a product stock field and update when invoices are sent',
    'stock_notifications' => 'Stock Notifications',
    'stock_notifications_help' => 'Send an email when the stock reaches the threshold',
    'vat' => 'VAT',
    'view_map' => 'View Map',
    'set_default_design' => 'Set Default Design',
    'purchase_order_issued_to' => 'Purchase Order issued to',
    'archive_task_status' => 'Archive Task Status',
    'delete_task_status' => 'Delete Task Status',
    'restore_task_status' => 'Restore Task Status',
    'lang_Hebrew' => 'Hebrew',
    'price_change_accepted' => 'Price change accepted',
    'price_change_failed' => 'Price change failed with code',
    'restore_purchases' => 'Restore Purchases',
    'activate' => 'Activate',
    'connect_apple' => 'Connect Apple',
    'disconnect_apple' => 'Disconnect Apple',
    'disconnected_apple' => 'Successfully disconnected Apple',
    'send_now' => 'Send Now',
    'received' => 'Received',
    'converted_to_expense' => 'Successfully converted to expense',
    'converted_to_expenses' => 'Successfully converted to expenses',
    'entity_removed' => 'This document has been removed, please contact the vendor for further information',
    'entity_removed_title' => 'Document no longer available',
    'field' => 'Field',
    'period' => 'Period',
    'fields_per_row' => 'Fields Per Row',
    'total_active_invoices' => 'Active Invoices',
    'total_outstanding_invoices' => 'Outstanding Invoices',
    'total_completed_payments' => 'Completed Payments',
    'total_refunded_payments' => 'Refunded Payments',
    'total_active_quotes' => 'Active Quotes',
    'total_approved_quotes' => 'Approved Quotes',
    'total_unapproved_quotes' => 'Unapproved Quotes',
    'total_logged_tasks' => 'Logged Tasks',
    'total_invoiced_tasks' => 'Invoiced Tasks',
    'total_paid_tasks' => 'Paid Tasks',
    'total_logged_expenses' => 'Logged Expenses',
    'total_pending_expenses' => 'Pending Expenses',
    'total_invoiced_expenses' => 'Invoiced Expenses',
    'total_invoice_paid_expenses' => 'Invoice Paid Expenses',
    'vendor_portal' => 'Vendor Portal',
    'send_code' => 'Send Code',
    'save_to_upload_documents' => 'Save the record to upload documents',
    'expense_tax_rates' => 'Expense Tax Rates',
    'invoice_item_tax_rates' => 'Invoice Item Tax Rates',
    'verified_phone_number' => 'Successfully verified phone number',
    'code_was_sent' => 'A code has been sent via SMS',
    'resend' => 'Resend',
    'verify' => 'Verify',
    'enter_phone_number' => 'Please provide a phone number',
    'invalid_phone_number' => 'Invalid phone number',
    'verify_phone_number' => 'Verify Phone Number',
    'verify_phone_number_help' => 'Please verify your phone number to send emails',
    'merged_clients' => 'Successfully merged clients',
    'merge_into' => 'Merge Into',
    'php81_required' => 'Note: v5.5 requires PHP 8.1',
    'bulk_email_purchase_orders' => 'Email Purchase Orders',
    'bulk_email_invoices' => 'Email Invoices',
    'bulk_email_quotes' => 'Email Quotes',
    'bulk_email_credits' => 'Email Credits',
    'archive_purchase_order' => 'Archive Purchase Order',
    'restore_purchase_order' => 'Restore Purchase Order',
    'delete_purchase_order' => 'Delete Purchase Order',
    'connect' => 'Connect',
    'mark_paid_payment_email' => 'Mark Paid Payment Email',
    'convert_to_project' => 'Convert to Project',
    'client_email' => 'Client Email',
    'invoice_task_project' => 'Invoice Task Project',
    'invoice_task_project_help' => 'Add the project to the invoice line items',
    'bulk_action' => 'Bulk Action',
    'phone_validation_error' => 'This mobile (cell) phone number is not valid, please enter in E.164 format',
    'transaction' => 'Transaction',
    'disable_2fa' => 'Disable 2FA',
    'change_number' => 'Change Number',
    'resend_code' => 'Resend Code',
    'base_type' => 'Base Type',
    'category_type' => 'Category Type',
    'bank_transaction' => 'Transaction',
    'bulk_print' => 'Print PDF',
    'vendor_postal_code' => 'Vendor Postal Code',
    'preview_location' => 'Preview Location',
    'bottom' => 'Bottom',
    'side' => 'Side',
    'pdf_preview' => 'PDF Preview',
    'long_press_to_select' => 'Long Press to Select',
    'purchase_order_item' => 'Purchase Order Item',
    'would_you_rate_the_app' => 'Would you like to rate the app?',
    'include_deleted' => 'Include Deleted',
    'include_deleted_help' => 'Include deleted records in reports',
    'due_on' => 'Due On',
    'browser_pdf_viewer' => 'Use Browser PDF Viewer',
    'browser_pdf_viewer_help' => 'Warning: Prevents interacting with app over the PDF',
    'converted_transactions' => 'Successfully converted transactions',
    'default_category' => 'Default Category',
    'connect_accounts' => 'Connect Accounts',
    'manage_rules' => 'Manage Rules',
    'search_category' => 'Search 1 Category',
    'search_categories' => 'Search :count Categories',
    'min_amount' => 'Min Amount',
    'max_amount' => 'Max Amount',
    'converted_transaction' => 'Successfully converted transaction',
    'convert_to_payment' => 'Convert to Payment',
    'deposit' => 'Deposit',
    'withdrawal' => 'Withdrawal',
    'deposits' => 'Deposits',
    'withdrawals' => 'Withdrawals',
    'matched' => 'Matched',
    'unmatched' => 'Unmatched',
    'create_credit' => 'Create Credit',
    'transactions' => 'Transactions',
    'new_transaction' => 'New Transaction',
    'edit_transaction' => 'Edit Transaction',
    'created_transaction' => 'Successfully created transaction',
    'updated_transaction' => 'Successfully updated transaction',
    'archived_transaction' => 'Successfully archived transaction',
    'deleted_transaction' => 'Successfully deleted transaction',
    'removed_transaction' => 'Successfully removed transaction',
    'restored_transaction' => 'Successfully restored transaction',
    'search_transaction' => 'Search Transaction',
    'search_transactions' => 'Search :count Transactions',
    'deleted_bank_account' => 'Successfully deleted bank account',
    'removed_bank_account' => 'Successfully removed bank account',
    'restored_bank_account' => 'Successfully restored bank account',
    'search_bank_account' => 'Search Bank Account',
    'search_bank_accounts' => 'Search :count Bank Accounts',
    'code_was_sent_to' => 'A code has been sent via SMS to :number',
    'verify_phone_number_2fa_help' => 'Please verify your phone number for 2FA backup',
    'enable_applying_payments_later' => 'Enable Applying Payments Later',
    'line_item_tax_rates' => 'Line Item Tax Rates',
    'show_tasks_in_client_portal' => 'Show Tasks in Client Portal',
    'notification_quote_expired_subject' => 'Quote :invoice has expired for :client',
    'notification_quote_expired' => 'The following Quote :invoice for client :client and :amount has now expired.',
    'auto_sync' => 'Auto Sync',
    'refresh_accounts' => 'Refresh Accounts',
    'upgrade_to_connect_bank_account' => 'Upgrade to Enterprise to connect your bank account',
    'click_here_to_connect_bank_account' => 'Click here to connect your bank account',
    'include_tax' => 'Include tax',
    'email_template_change' => 'E-mail template body can be changed on',
    'task_update_authorization_error' => 'Insufficient permissions, or task may be locked',
    'cash_vs_accrual' => 'Accrual accounting',
    'cash_vs_accrual_help' => 'Turn on for accrual reporting, turn off for cash basis reporting.',
    'expense_paid_report' => 'Expensed reporting',
    'expense_paid_report_help' => 'Turn on for reporting all expenses, turn off for reporting only paid expenses',
    'online_payment_email_help' => 'Send an email when an online payment is made',
    'manual_payment_email_help' => 'Send an email when manually entering a payment',
    'mark_paid_payment_email_help' => 'Send an email when marking an invoice as paid',
    'linked_transaction' => 'Successfully linked transaction',
    'link_payment' => 'Link Payment',
    'link_expense' => 'Link Expense',
    'lock_invoiced_tasks' => 'Lock Invoiced Tasks',
    'lock_invoiced_tasks_help' => 'Prevent tasks from being edited once invoiced',
    'registration_required_help' => 'Require clients to register',
    'use_inventory_management' => 'Use Inventory Management',
    'use_inventory_management_help' => 'Require products to be in stock',
    'optional_products' => 'Optional Products',
    'optional_recurring_products' => 'Optional Recurring Products',
    'convert_matched' => 'Convert',
    'auto_billed_invoice' => 'Successfully queued invoice to be auto-billed',
    'auto_billed_invoices' => 'Successfully queued invoices to be auto-billed',
    'operator' => 'Operator',
    'value' => 'Value',
    'is' => 'Is',
    'contains' => 'Contains',
    'starts_with' => 'Starts with',
    'is_empty' => 'Is empty',
    'add_rule' => 'Add Rule',
    'match_all_rules' => 'Match All Rules',
    'match_all_rules_help' => 'All criteria needs to match for the rule to be applied',
    'auto_convert_help' => 'Automatically convert matched transactions to expenses',
    'rules' => 'Rules',
    'transaction_rule' => 'Transaction Rule',
    'transaction_rules' => 'Transaction Rules',
    'new_transaction_rule' => 'New Transaction Rule',
    'edit_transaction_rule' => 'Edit Transaction Rule',
    'created_transaction_rule' => 'Successfully created rule',
    'updated_transaction_rule' => 'Successfully updated transaction rule',
    'archived_transaction_rule' => 'Successfully archived transaction rule',
    'deleted_transaction_rule' => 'Successfully deleted transaction rule',
    'removed_transaction_rule' => 'Successfully removed transaction rule',
    'restored_transaction_rule' => 'Successfully restored transaction rule',
    'search_transaction_rule' => 'Search Transaction Rule',
    'search_transaction_rules' => 'Search Transaction Rules',
    'payment_type_Interac E-Transfer' => 'Interac E-Transfer',
    'delete_bank_account' => 'Delete Bank Account',
    'archive_transaction' => 'Archive Transaction',
    'delete_transaction' => 'Delete Transaction',
    'otp_code_message' => 'We have sent a code to :email enter this code to proceed.',
    'otp_code_subject' => 'Your one time passcode code',
    'otp_code_body' => 'Your one time passcode is :code',
    'delete_tax_rate' => 'Delete Tax Rate',
    'restore_tax_rate' => 'Restore Tax Rate',
    'company_backup_file' => 'Select company backup file',
    'company_backup_file_help' => 'Please upload the .zip file used to create this backup.',
    'backup_restore' => 'Backup | Restore',
    'export_company' => 'Create company backup',
    'backup' => 'Backup',
    'notification_purchase_order_created_body' => 'The following purchase_order :purchase_order was created for vendor :vendor for :amount.',
    'notification_purchase_order_created_subject' => 'Purchase Order :purchase_order was created for :vendor',
    'notification_purchase_order_sent_subject' => 'Purchase Order :purchase_order was sent to :vendor',
    'notification_purchase_order_sent' => 'The following vendor :vendor was emailed Purchase Order :purchase_order for :amount.',
    'subscription_blocked' => 'This product is a restricted item, please contact the vendor for further information.',
    'subscription_blocked_title' => 'Product not available.',
    'purchase_order_created' => 'Purchase Order Created',
    'purchase_order_sent' => 'Purchase Order Sent',
    'purchase_order_viewed' => 'Purchase Order Viewed',
    'purchase_order_accepted' => 'Purchase Order Accepted',
    'credit_payment_error' => 'The credit amount can not be greater than the payment amount',
    'convert_payment_currency_help' => 'Set an exchange rate when entering a manual payment',
    'convert_expense_currency_help' => 'Set an exchange rate when creating an expense',
    'matomo_url' => 'Matomo URL',
    'matomo_id' => 'Matomo Id',
    'action_add_to_invoice' => 'Add To Invoice',
    'danger_zone' => 'Danger Zone',
    'import_completed' => 'Import completed',
    'client_statement_body' => 'Your statement from :start_date to :end_date is attached.',
    'email_queued' => 'Email queued',
    'clone_to_recurring_invoice' => 'Clone to Recurring Invoice',
    'inventory_threshold' => 'Inventory Threshold',
    'emailed_statement' => 'Successfully queued statement to be sent',
    'show_email_footer' => 'Show Email Footer',
    'invoice_task_hours' => 'Invoice Task Hours',
    'invoice_task_hours_help' => 'Add the hours to the invoice line items',
    'auto_bill_standard_invoices' => 'Auto Bill Standard Invoices',
    'auto_bill_recurring_invoices' => 'Auto Bill Recurring Invoices',
    'email_alignment' => 'Email Alignment',
    'pdf_preview_location' => 'PDF Preview Location',
    'mailgun' => 'Mailgun',
    'brevo' => 'Brevo',
    'postmark' => 'Postmark',
    'microsoft' => 'Microsoft',
    'click_plus_to_create_record' => 'Click + to create a record',
    'last365_days' => 'Last 365 Days',
    'import_design' => 'Import Design',
    'imported_design' => 'Successfully imported design',
    'invalid_design' => 'The design is invalid, the :value section is missing',
    'setup_wizard_logo' => 'Would you like to upload your logo?',
    'installed_version' => 'Installed Version',
    'notify_vendor_when_paid' => 'Notify Vendor When Paid',
    'notify_vendor_when_paid_help' => 'Send an email to the vendor when the expense is marked as paid',
    'update_payment' => 'Update Payment',
    'markup' => 'Markup',
    'unlock_pro' => 'Unlock Pro',
    'upgrade_to_paid_plan_to_schedule' => 'Upgrade to a paid plan to create schedules',
    'next_run' => 'Next Run',
    'all_clients' => 'All Clients',
    'show_aging_table' => 'Show Aging Table',
    'show_payments_table' => 'Show Payments Table',
    'only_clients_with_invoices' => 'Only Clients with Invoices',
    'email_statement' => 'Email Statement',
    'once' => 'Once',
    'schedules' => 'Schedules',
    'new_schedule' => 'New Schedule',
    'edit_schedule' => 'Edit Schedule',
    'created_schedule' => 'Successfully created schedule',
    'updated_schedule' => 'Successfully updated schedule',
    'archived_schedule' => 'Successfully archived schedule',
    'deleted_schedule' => 'Successfully deleted schedule',
    'removed_schedule' => 'Successfully removed schedule',
    'restored_schedule' => 'Successfully restored schedule',
    'search_schedule' => 'Search Schedule',
    'search_schedules' => 'Search Schedules',
    'update_product' => 'Update Product',
    'create_purchase_order' => 'Create Purchase Order',
    'update_purchase_order' => 'Update Purchase Order',
    'sent_invoice' => 'Sent Invoice',
    'sent_quote' => 'Sent Quote',
    'sent_credit' => 'Sent Credit',
    'sent_purchase_order' => 'Sent Purchase Order',
    'image_url' => 'Image URL',
    'max_quantity' => 'Max Quantity',
    'test_url' => 'Test URL',
    'auto_bill_help_off' => 'Option is not shown',
    'auto_bill_help_optin' => 'Option is shown but not selected',
    'auto_bill_help_optout' => 'Option is shown and selected',
    'auto_bill_help_always' => 'Option is not shown',
    'view_all' => 'View All',
    'edit_all' => 'Edit All',
    'accept_purchase_order_number' => 'Accept Purchase Order Number',
    'accept_purchase_order_number_help' => 'Enable clients to provide a PO number when approving a quote',
    'from_email' => 'From Email',
    'show_preview' => 'Show Preview',
    'show_paid_stamp' => 'Show Paid Stamp',
    'show_shipping_address' => 'Show Shipping Address',
    'no_documents_to_download' => 'There are no documents in the selected records to download',
    'pixels' => 'Pixels',
    'logo_size' => 'Logo Size',
    'failed' => 'Failed',
    'client_contacts' => 'Client Contacts',
    'sync_from' => 'Sync From',
    'gateway_payment_text' => 'Invoices: :invoices for :amount for client :client',
    'gateway_payment_text_no_invoice' => 'Payment with no invoice for amount :amount for client :client',
    'click_to_variables' => 'Click here to see all variables.',
    'ship_to' => 'Ship to',
    'stripe_direct_debit_details' => 'Please transfer into the nominated bank account above.',
    'branch_name' => 'Branch Name',
    'branch_code' => 'Branch Code',
    'bank_name' => 'Bank Name',
    'bank_code' => 'Bank Code',
    'bic' => 'BIC',
    'change_plan_description' => 'Upgrade or downgrade your current plan.',
    'add_company_logo' => 'Add Logo',
    'add_stripe' => 'Add Stripe',
    'invalid_coupon' => 'Invalid Coupon',
    'no_assigned_tasks' => 'No billable tasks for this project',
    'authorization_failure' => 'Insufficient permissions to perform this action',
    'authorization_sms_failure' => 'Please verify your account to send emails.',
    'white_label_body' => 'Thank you for purchasing a white label license. <br><br> Your license key is: <br><br> :license_key <br><br> You can manage your license here: https://invoiceninja.invoicing.co/client/login',
    'payment_type_Klarna' => 'Klarna',
    'payment_type_Interac E Transfer' => 'Interac E Transfer',
    'xinvoice_payable' => 'Payable within :payeddue days net until :paydate',
    'xinvoice_no_buyers_reference' => "No buyer's reference given",
    'xinvoice_online_payment' => 'The invoice needs to be paid online via the provided link',
    'pre_payment' => 'Pre Payment',
    'number_of_payments' => 'Number of payments',
    'number_of_payments_helper' => 'The number of times this payment will be made',
    'pre_payment_indefinitely' => 'Continue until cancelled',
    'notification_payment_emailed' => 'Payment :payment was emailed to :client',
    'notification_payment_emailed_subject' => 'Payment :payment was emailed',
    'record_not_found' => 'Record not found',
    'minimum_payment_amount' => 'Minimum Payment Amount',
    'client_initiated_payments' => 'Client Initiated Payments',
    'client_initiated_payments_help' => 'Support making a payment in the client portal without an invoice',
    'share_invoice_quote_columns' => 'Share Invoice/Quote Columns',
    'cc_email' => 'CC Email',
    'payment_balance' => 'Payment Balance',
    'view_report_permission' => 'Allow user to access the reports, data is limited to available permissions',
    'activity_138' => 'Payment :payment was emailed to :client',
    'one_time_products' => 'One-Time Products',
    'optional_one_time_products' => 'Optional One-Time Products',
    'required' => 'Required',
    'hidden' => 'Hidden',
    'payment_links' => 'Payment Links',
    'payment_link' => 'Payment Link',
    'new_payment_link' => 'New Payment Link',
    'edit_payment_link' => 'Edit Payment Link',
    'created_payment_link' => 'Successfully created payment link',
    'updated_payment_link' => 'Successfully updated payment link',
    'archived_payment_link' => 'Successfully archived payment link',
    'deleted_payment_link' => 'Successfully deleted payment link',
    'removed_payment_link' => 'Successfully removed payment link',
    'restored_payment_link' => 'Successfully restored payment link',
    'search_payment_link' => 'Search 1 Payment Link',
    'search_payment_links' => 'Search :count Payment Links',
    'increase_prices' => 'Increase Prices',
    'update_prices' => 'Update Prices',
    'incresed_prices' => 'Successfully queued prices to be increased',
    'updated_prices' => 'Successfully queued prices to be updated',
    'api_token' => 'API Token',
    'api_key' => 'API Key',
    'endpoint' => 'Endpoint',
    'not_billable' => 'Not Billable',
    'allow_billable_task_items' => 'Allow Billable Task Items',
    'allow_billable_task_items_help' => 'Enable configuring which task items are billed',
    'show_task_item_description' => 'Show Task Item Description',
    'show_task_item_description_help' => 'Enable specifying task item descriptions',
    'email_record' => 'Email Record',
    'invoice_product_columns' => 'Invoice Product Columns',
    'quote_product_columns' => 'Quote Product Columns',
    'vendors' => 'Vendors',
    'product_sales' => 'Product Sales',
    'user_sales_report_header' => 'User sales report for client/s :client from :start_date to :end_date',
    'client_balance_report' => 'Customer balance report',
    'client_sales_report' => 'Customer sales report',
    'user_sales_report' => 'User sales report',
    'aged_receivable_detailed_report' => 'Aged Receivable Detailed Report',
    'aged_receivable_summary_report' => 'Aged Receivable Summary Report',
    'taxable_amount' => 'Taxable Amount',
    'tax_summary' => 'Tax Summary',
    'oauth_mail' => 'OAuth / Mail',
    'preferences' => 'Preferences',
    'analytics' => 'Analytics',
    'reduced_rate' => 'Reduced Rate',
    'tax_all' => 'Tax All',
    'tax_selected' => 'Tax Selected',
    'version' => 'version',
    'seller_subregion' => 'Seller Subregion',
    'calculate_taxes' => 'Calculate Taxes',
    'calculate_taxes_help' => 'Automatically calculate taxes when saving invoices',
    'link_expenses' => 'Link Expenses',
    'converted_client_balance' => 'Converted Client Balance',
    'converted_payment_balance' => 'Converted Payment Balance',
    'total_hours' => 'Total Hours',
    'date_picker_hint' => 'Use +days to set the date in the future',
    'app_help_link' => 'More information ',
    'here' => 'here',
    'industry_Restaurant & Catering' => 'Restaurant & Catering',
    'show_credits_table' => 'Show Credits Table',
    'manual_payment' => 'Payment Manual',
    'tax_summary_report' => 'Tax Summary Report',
    'tax_category' => 'Tax Category',
    'physical_goods' => 'Physical Goods',
    'digital_products' => 'Digital Products',
    'services' => 'Services',
    'shipping' => 'Shipping',
    'tax_exempt' => 'Tax Exempt',
    'late_fee_added_locked_invoice' => 'Late fee for invoice :invoice added on :date',
    'lang_Khmer' => 'Khmer',
    'routing_id' => 'Routing ID',
    'enable_e_invoice' => 'Enable E-Invoice',
    'e_invoice_type' => 'E-Invoice Type',
    'reduced_tax' => 'Reduced Tax',
    'override_tax' => 'Override Tax',
    'zero_rated' => 'Zero Rated',
    'reverse_tax' => 'Reverse Tax',
    'updated_tax_category' => 'Successfully updated the tax category',
    'updated_tax_categories' => 'Successfully updated the tax categories',
    'set_tax_category' => 'Set Tax Category',
    'payment_manual' => 'Payment Manual',
    'expense_payment_type' => 'Expense Payment Type',
    'payment_type_Cash App' => 'Cash App',
    'rename' => 'Rename',
    'renamed_document' => 'Successfully renamed document',
    'e_invoice' => 'E-Invoice',
    'light_dark_mode' => 'Light/Dark Mode',
    'activities' => 'Activities',
    'recent_transactions' => "Recent Transactions",
    'country_Palestine' => "Palestine",
    'country_Taiwan' => 'Taiwan',
    'duties' => 'Duties',
    'order_number' => 'Order Number',
    'order_id' => 'Order',
    'total_invoices_outstanding' => 'Total Invoices Outstanding',
    'recent_activity' => 'Recent Activity',
    'enable_auto_bill' => 'Enable auto billing',
    'email_count_invoices' => 'Email :count invoices',
    'invoice_task_item_description' => 'Invoice Task Item Description',
    'invoice_task_item_description_help' => 'Add the item description to the invoice line items',
    'next_send_time' => 'Next Send Time',
    'uploaded_certificate' => 'Successfully uploaded certificate',
    'certificate_set' => 'Certificate set',
    'certificate_not_set' => 'Certificate not set',
    'passphrase_set' => 'Passphrase set',
    'passphrase_not_set' => 'Passphrase not set',
    'upload_certificate' => 'Upload Certificate',
    'certificate_passphrase' => 'Certificate Passphrase',
    'valid_vat_number' => 'Valid VAT Number',
    'react_notification_link' => 'React Notification Links',
    'react_notification_link_help' => 'Admin emails will contain links to the react application',
    'show_task_billable' => 'Show Task Billable',
    'credit_item' => 'Credit Item',
    'drop_file_here' => 'Drop file here',
    'files' => 'Files',
    'camera' => 'Camera',
    'gallery' => 'Gallery',
    'project_location' => 'Project Location',
    'add_gateway_help_message' => 'Add a payment gateway (ie. Stripe, WePay or PayPal) to accept online payments',
    'lang_Hungarian' => 'Hungarian',
    'use_mobile_to_manage_plan' => 'Use your phone subscription settings to manage your plan',
    'item_tax3' => 'Item Tax3',
    'item_tax_rate1' => 'Item Tax Rate 1',
    'item_tax_rate2' => 'Item Tax Rate 2',
    'item_tax_rate3' => 'Item Tax Rate 3',
    'buy_price' => 'Buy Price',
    'country_Macedonia' => 'Macedonia',
    'admin_initiated_payments' => 'Admin Initiated Payments',
    'admin_initiated_payments_help' => 'Support entering a payment in the admin portal without an invoice',
    'paid_date' => 'Paid Date',
    'downloaded_entities' => 'An email will be sent with the PDFs',
    'lang_French - Swiss' => 'French - Swiss',
    'currency_swazi_lilangeni' => 'Swazi Lilangeni',
    'income' => 'Income',
    'amount_received_help' => 'Enter a value here if the total amount received was MORE than the invoice amount, or when recording a payment with no invoices. Otherwise this field should be left blank.',
    'vendor_phone' => 'Vendor Phone',
    'mercado_pago' => 'Mercado Pago',
    'mybank' => 'MyBank',
    'paypal_paylater' => 'Pay in 4',
    'district' => 'District',
    'region' => 'Region',
    'county' => 'County',
    'tax_details' => 'Tax Details',
    'activity_10_online' => ':contact made payment :payment for invoice :invoice for :client',
    'activity_10_manual' => ':user entered payment :payment for invoice :invoice for :client',
    'default_payment_type' => 'Default Payment Type',
    'number_precision' => 'Number precision',
    'number_precision_help' => 'Controls the number of decimals supported in the interface',
    'is_tax_exempt' => 'Tax Exempt',
    'drop_files_here' => 'Drop files here',
    'upload_files' => 'Upload Files',
    'download_e_invoice' => 'Download E-Invoice',
    'download_e_credit' => 'Download E-Credit',
    'download_e_quote' => 'Download E-Quote',
    'triangular_tax_info' => 'Intra-community triangular transaction',
    'intracommunity_tax_info' => 'Tax-free intra-community delivery',
    'reverse_tax_info' => 'Please note that this supply is subject to reverse charge',
    'currency_nicaraguan_cordoba' => 'Nicaraguan Córdoba',
    'public' => 'Public',
    'private' => 'Private',
    'image' => 'Image',
    'other' => 'Other',
    'linked_to' => 'Linked To',
    'file_saved_in_path' => 'The file has been saved in :path',
    'unlinked_transactions' => 'Successfully unlinked :count transactions',
    'unlinked_transaction' => 'Successfully unlinked transaction',
    'view_dashboard_permission' => 'Allow user to access the dashboard, data is limited to available permissions',
    'marked_sent_credits' => 'Successfully marked credits sent',
    'show_document_preview' => 'Show Document Preview',
    'cash_accounting' => 'Cash accounting',
    'click_or_drop_files_here' => 'Click or drop files here',
    'set_public' => 'Set public',
    'set_private' => 'Set private',
    'individual' => 'Individual',
    'business' => 'Business',
    'partnership' => 'Partnership',
    'trust' => 'Trust',
    'charity' => 'Charity',
    'government' => 'Government',
    'in_stock_quantity' => 'Stock quantity',
    'vendor_contact' => 'Vendor Contact',
    'expense_status_4' => 'Unpaid',
    'expense_status_5' => 'Paid',
    'ziptax_help' => 'Note: this feature requires a Zip-Tax API key to lookup US sales tax by address',
    'cache_data' => 'Cache Data',
    'unknown' => 'Unknown',
    'webhook_failure' => 'Webhook Failure',
    'email_opened' => 'Email Opened',
    'email_delivered' => 'Email Delivered',
    'log' => 'Log',
    'classification' => 'Classification',
    'stock_quantity_number' => 'Stock :quantity',
    'upcoming' => 'Upcoming',
    'client_contact' => 'Client Contact',
    'uncategorized' => 'Uncategorized',
    'login_notification' => 'Login Notification',
    'login_notification_help' => 'Sends an email notifying that a login has taken place.',
    'payment_refund_receipt' => 'Payment Refund Receipt # :number',
    'payment_receipt' => 'Payment Receipt # :number',
    'load_template_description' => 'The template will be applied to following:',
    'run_template' => 'Run Template',
    'statement_design' => 'Statement Design',
    'delivery_note_design' => 'Delivery Note Design',
    'payment_receipt_design' => 'Payment Receipt Design',
    'payment_refund_design' => 'Payment Refund Design',
    'task_extension_banner' => 'Add the Chrome extension to manage your tasks',
    'watch_video' => 'Watch Video',
    'view_extension' => 'View Extension',
    'reactivate_email' => 'Reactivate Email',
    'email_reactivated' => 'Successfully reactivated email',
    'template_help' => 'Enable using the design as a template',
    'quarter' => 'Quarter',
    'item_description' => 'Item Description',
    'task_item' => 'Task Item',
    'record_state' => 'Record State',
    'save_files_to_this_folder' => 'Save files to this folder',
    'downloads_folder' => 'Downloads Folder',
    'total_invoiced_quotes' => 'Invoiced Quotes',
    'total_invoice_paid_quotes' => 'Invoice Paid Quotes',
    'downloads_folder_does_not_exist' => 'The downloads folder does not exist :value',
    'user_logged_in_notification' => 'User Logged in Notification',
    'user_logged_in_notification_help' => 'Send an email when logging in from a new location',
    'payment_email_all_contacts' => 'Payment Email To All Contacts',
    'payment_email_all_contacts_help' => 'Sends the payment email to all contacts when enabled',
    'add_line' => 'Add Line',
    'activity_139' => 'Expense :expense notification sent to :contact',
    'vendor_notification_subject' => 'Confirmation of payment :amount sent to :vendor',
    'vendor_notification_body' => 'Payment processed for :amount dated :payment_date. <br>[Transaction Reference: :transaction_reference]',
    'receipt' => 'Receipt',
    'charges' => 'Charges',
    'email_report' => 'Email Report',
    'payment_type_Pay Later' => 'Pay Later',
    'payment_type_credit' => 'Payment Type Credit',
    'payment_type_debit' => 'Payment Type Debit',
    'send_emails_to' => 'Send Emails To',
    'primary_contact' => 'Primary Contact',
    'all_contacts' => 'All Contacts',
    'insert_below' => 'Insert Below',
    'nordigen_handler_subtitle' => 'Bank account authentication. Selecting your institution to complete the request with your account credentials.',
    'nordigen_handler_error_heading_unknown' => 'An error has occurred',
    'nordigen_handler_error_contents_unknown' => 'An unknown error has occurred! Reason:',
    'nordigen_handler_error_heading_token_invalid' => 'Invalid Token',
    'nordigen_handler_error_contents_token_invalid' => 'The provided token was invalid. Contact support for help, if this issue persists.',
    'nordigen_handler_error_heading_account_config_invalid' => 'Missing Credentials',
    'nordigen_handler_error_contents_account_config_invalid' => 'Invalid or missing credentials for Gocardless Bank Account Data. Contact support for help, if this issue persists.',
    'nordigen_handler_error_heading_not_available' => 'Not Available',
    'nordigen_handler_error_contents_not_available' => 'Feature unavailable, Enterprise Plan only.',
    'nordigen_handler_error_heading_institution_invalid' => 'Invalid Institution',
    'nordigen_handler_error_contents_institution_invalid' => 'The provided institution-id is invalid or no longer valid.',
    'nordigen_handler_error_heading_ref_invalid' => 'Invalid Reference',
    'nordigen_handler_error_contents_ref_invalid' => 'GoCardless did not provide a valid reference. Please run flow again and contact support, if this issue persists.',
    'nordigen_handler_error_heading_eua_failure' => 'EUA Failure',
    'nordigen_handler_error_contents_eua_failure' => 'An error occurred during End User Agreement creation:',
    'nordigen_handler_error_heading_not_found' => 'Invalid Requisition',
    'nordigen_handler_error_contents_not_found' => 'GoCardless did not provide a valid reference. Please run flow again and contact support, if this issue persists.',
    'nordigen_handler_error_heading_requisition_invalid_status' => 'Not Ready',
    'nordigen_handler_error_contents_requisition_invalid_status' => 'You called this site too early. Please finish authorization and refresh this page. Contact support for help, if this issue persists.',
    'nordigen_handler_error_heading_requisition_no_accounts' => 'No Accounts selected',
    'nordigen_handler_error_contents_requisition_no_accounts' => 'The service has not returned any valid accounts. Consider restarting the flow.',
    'nordigen_handler_restart' => 'Restart flow.',
    'nordigen_handler_return' => 'Return to application.',
    'lang_Lao' => 'Lao',
    'currency_lao_kip' => 'Lao kip',
    'yodlee_regions' => 'Regions: USA, UK, Australia & India',
    'nordigen_regions' => 'Regions: Europe & UK',
    'select_provider' => 'Select Provider',
    'nordigen_requisition_subject' => 'Requisition expired, please reauthenticate.',
    'nordigen_requisition_body' => 'Access to bank account feeds has expired as set in End User Agreement. <br><br>Please log into Invoice Ninja and re-authenticate with your banks to continue receiving transactions.',
    'participant' => 'Participant',
    'participant_name' => 'Participant name',
    'client_unsubscribed' => 'Client unsubscribed from emails.',
    'client_unsubscribed_help' => 'Client :client has unsubscribed from your e-mails. The client needs to consent to receive future emails from you.',
    'resubscribe' => 'Resubscribe',
    'subscribe' => 'Subscribe',
    'subscribe_help' => 'You are currently subscribed and will continue to receive email communications.',
    'unsubscribe_help' => 'You are currently not subscribed, and therefore, will not receive emails at this time.',
    'notification_purchase_order_bounced' => 'We were unable to deliver Purchase Order :invoice to :contact. <br><br> :error',
    'notification_purchase_order_bounced_subject' => 'Unable to deliver Purchase Order :invoice',
    'show_pdfhtml_on_mobile' => 'Display HTML version of entity when viewing on mobile',
    'show_pdfhtml_on_mobile_help' => 'For improved visualization, displays a HTML version of the invoice/quote when viewing on mobile.',
    'please_select_an_invoice_or_credit' => 'Please select an invoice or credit',
    'mobile_version' => 'Mobile Version',
    'venmo' => 'Venmo',
    'my_bank' => 'MyBank',
    'pay_later' => 'Pay Later',
    'local_domain' => 'Local Domain',
    'verify_peer' => 'Verify Peer',
    'nordigen_help' => 'Note: connecting an account requires a GoCardless/Nordigen API key',
    'ar_detailed' => 'Accounts Receivable Detailed',
    'ar_summary' => 'Accounts Receivable Summary',
    'client_sales' => 'Client Sales',
    'user_sales' => 'User Sales',
    'iframe_url' => 'iFrame URL',
    'user_unsubscribed' => 'User unsubscribed from emails :link',
    'out_of_stock' => 'Out of stock',
    'step_dependency_fail' => 'Component ":step" requires at least one of it\'s dependencies (":dependencies") in the list.',
    'step_dependency_order_fail' => 'Component ":step" depends on ":dependency". Make component(s) order is correct.',
    'step_authentication_fail' => 'You must include at least one of authentication methods.',
    'auth.login' => 'Login',
    'auth.login-or-register' => 'Login or Register',
    'auth.register' => 'Register',
    'cart' => 'Cart',
    'methods' => 'Methods',
    'rff' => 'Required fields form',
    'add_step' => 'Add step',
    'steps' => 'Steps',
    'steps_order_help' =>  'The order of the steps is important. The first step should not depend on any other step. The second step should depend on the first step, and so on.',
    'other_steps' => 'Other steps',
    'use_available_payments' => 'Use Available Payments',
    'test_email_sent' => 'Successfully sent email',
    'gateway_type' => 'Gateway Type',
    'save_template_body' => 'Would you like to save this import mapping as a template for future use?',
    'save_as_template' => 'Save Template Mapping',
    'checkout_only_for_existing_customers' => 'Checkout is enabled only for existing customers. Please login with existing account to checkout.',
    'checkout_only_for_new_customers' => 'Checkout is enabled only for new customers. Please register a new account to checkout.',
    'auto_bill_standard_invoices_help' => 'Auto bill standard invoices on the due date',
    'auto_bill_on_help' => 'Auto bill on send date OR due date (recurring invoices)',
    'use_available_credits_help' => 'Apply any credit balances to payments prior to charging a payment method',
    'use_unapplied_payments' => 'Use unapplied payments',
    'use_unapplied_payments_help' => 'Apply any payment balances prior to charging a payment method',
    'payment_terms_help' => 'The number of days after the invoice date that payment is due',
    'payment_type_help' => 'The default payment type to be used for payments',
    'quote_valid_until_help' => 'The number of days that the quote is valid for',
    'expense_payment_type_help' => 'The default expense payment type to be used',
    'paylater' => 'Pay in 4',
    'payment_provider' => 'Payment Provider',
    'select_email_provider' => 'Set your email as the sending user',
    'purchase_order_items' => 'Purchase Order Items',
    'csv_rows_length' => 'No data found in this CSV file',
    'accept_payments_online' => 'Accept Payments Online',
    'all_payment_gateways' => 'View all payment gateways',
    'product_cost' => 'Product cost',
    'duration_words' => 'Duration in words',
    'upcoming_recurring_invoices' => 'Upcoming Recurring Invoices',
    'shipping_country_id' => 'Shipping Country',
    'show_table_footer' => 'Show table footer',
    'show_table_footer_help' => 'Displays the totals in the footer of the table',
    'total_invoices' => 'Total Invoices',
    'add_to_group' => 'Add to group',
    'check_credentials' => 'Check Credentials',
    'valid_credentials' => 'Credentials are valid',
    'e_quote' => 'E-Quote',
    'e_credit' => 'E-Credit',
    'e_purchase_order' => 'E-Purchase Order',
    'e_quote_type' => 'E-Quote Type',
    'unlock_unlimited_clients' => 'Please upgrade to unlock unlimited clients!',
    'download_e_purchase_order' => 'Download E-Purchase Order',
    'flutter_web_warning' => 'We recommend using the new web app or the desktop app for the best performance',
    'rappen_rounding' => 'Rappen Rounding',
    'rappen_rounding_help' => 'Round amount to 5 cents',
    'assign_group' => 'Assign group',
    'paypal_advanced_cards' => 'Advanced Card Payments',
    'local_domain_help' => 'EHLO domain (optional)',
    'port_help' => 'ie. 25,587,465',
    'host_help' => 'ie. smtp.gmail.com',
    'always_show_required_fields' => 'Always show required fields form',
    'always_show_required_fields_help' => 'Displays the required fields form always at checkout',
    'advanced_cards' => 'Advanced Cards',
    'activity_140' => 'Statement sent to :client',
    'invoice_net_amount' => 'Invoice Net Amount',
    'round_to_minutes' => 'Round To Minutes',
    '1_second' => '1 Second',
    '1_minute' => '1 Minute',
    '5_minutes' => '5 Minutes',
    '15_minutes' => '15 Minutes',
    '30_minutes' => '30 Minutes',
    '1_hour' => '1 Hour',
    '1_day' => '1 Day',
    'round_tasks' => 'Task Rounding Direction',
    'round_tasks_help' => 'Round task times up or down.',
    'direction' => 'Direction',
    'round_up' => 'Round Up',
    'round_down' => 'Round Down',
    'task_round_to_nearest' => 'Round To Nearest',
    'task_round_to_nearest_help' => 'The interval to round the task to.',
    'bulk_updated' => 'Successfully updated data',
    'bulk_update' => 'Bulk Update',
    'calculate' => 'Calculate',
    'sum' => 'Sum',
    'money' => 'Money',
    'web_app' => 'Web App',
    'desktop_app' => 'Desktop App',
    'disconnected' => 'Disconnected',
    'reconnect' => 'Reconnect',
    'e_invoice_settings' => 'E-Invoice Settings',
    'btcpay_refund_subject' => 'Refund of your invoice via BTCPay',
    'btcpay_refund_body' => 'A refund intended for you has been issued. To claim it via BTCPay, please click on this link:',
    'currency_mauritanian_ouguiya' => 'Mauritanian Ouguiya',
    'currency_bhutan_ngultrum' => 'Bhutan Ngultrum',
    'end_of_month' => 'End Of Month',
    'merge_e_invoice_to_pdf' => 'Merge E-Invoice and PDF',
    'task_assigned_subject' => 'New task assignment [Task :task] [ :date ]',
    'task_assigned_body' => 'You have been assigned task :task <br><br> Description: :description <br><br> Client: :client',
    'activity_141' => 'User :user entered note: :notes',
    'quote_reminder_subject' => 'Reminder: Quote :quote from :company',
    'quote_reminder_message' => 'Reminder for quote :number for :amount',
    'quote_reminder1' => 'First Quote Reminder',
    'before_valid_until_date' => 'Before the valid until date',
    'after_valid_until_date' => 'After the valid until date',
    'after_quote_date' => 'After the quote date',
    'remind_quote' => 'Remind Quote',
    'end_of_month' => 'End Of Month',
    'tax_currency_mismatch' => 'Tax currency is different from invoice currency',
    'edocument_import_already_exists' => 'The invoice has already been imported on :date',
    'before_valid_until' => 'Before the valid until',
    'after_valid_until' => 'After the valid until',
    'task_assigned_notification' => 'Task Assigned Notification',
    'task_assigned_notification_help' => 'Send an email when a task is assigned',
    'invoices_locked_end_of_month' => 'Invoices are locked at the end of the month',
    'referral_url' => 'Referral URL',
    'add_comment' => 'Add Comment',
    'added_comment' => 'Successfully saved comment',
    'tickets' => 'Tickets',
    'assigned_group' => 'Successfully assigned group',
    'merge_to_pdf' => 'Merge to PDF',
    'latest_requires_php_version' => 'Note: the latest version requires PHP :version',
    'auto_expand_product_table_notes' => 'Automatically expand products table notes',
    'auto_expand_product_table_notes_help' => 'Automatically expands the notes section within the products table to display more lines.',
    'institution_number' => 'Institution Number',
    'transit_number' => 'Transit Number',
    'personal' => 'Personal',
    'address_information' => 'Address Information',
    'enter_the_information_for_the_bank_account' => 'Enter the Information for the Bank Account',
    'account_holder_information' => 'Account Holder Information',
    'enter_information_for_the_account_holder' => 'Enter Information for the Account Holder',
    'customer_type' => 'Customer Type',
    'process_date' => 'Process Date',
    'forever_free' => 'Forever Free',
    'comments_only' => 'Comments Only',
    'payment_balance_on_file' => 'Payment Balance On File',
    'ubl_email_attachment_help' => 'For more e-invoice settings please navigate :here',
    'stop_task_to_add_task_entry' => 'You need to stop the task before adding a new item.',
    'xml_file' => 'XML File',
    'one_page_checkout' => 'One-Page Checkout',
    'one_page_checkout_help' => 'Enable the new single page payment flow',
    'applies_to' => 'Applies To',
    'accept_purchase_order' => 'Accept Purchase Order',
    'round_to_seconds' => 'Round To Seconds',
    'activity_142' => 'Quote :quote reminder 1 sent',
    'activity_143' => 'Auto Bill succeeded for invoice :invoice',
    'activity_144' => 'Auto Bill failed for invoice :invoice. :notes',
    'activity_145' => 'E-Invoice :invoice for :client was sent. :notes',
    'payment_failed' => 'Payment Failed',
    'ssl_host_override' => 'SSL Host Override',
    'upload_logo_short' => 'Upload Logo',
    'country_Melilla' => 'Melilla',
    'country_Ceuta' => 'Ceuta',
    'country_Canary Islands' => 'Canary Islands',
    'lang_Vietnamese' => 'Vietnamese',
    'invoice_status_changed' => 'Please note that the status of your invoice has been updated. We recommend refreshing the page to view the most current version.',
    'no_unread_notifications' => 'You’re all caught up! No new notifications.',
    'how_to_import_data' => 'How to import data',
    'download_example_file' => 'Download example file',
    'expense_mailbox' => 'Inbound e-mail address',
    'expense_mailbox_help' => 'The inbound email address which accepts expense documents. ie. <EMAIL>',
    'expense_mailbox_active' => 'Expense Mailbox',
    'expense_mailbox_active_help' => 'Enables processing of documents such as receipts for expense reporting',
    'inbound_mailbox_allow_company_users' => 'Allow Company Senders',
    'inbound_mailbox_allow_company_users_help' => 'Allows users within the company to send expense documents.',
    'inbound_mailbox_allow_vendors' => 'Allow Vendor Senders',
    'inbound_mailbox_allow_vendors_help' => 'Allows company vendors to send expense documents',
    'inbound_mailbox_allow_clients' => 'Allow Client Senders',
    'inbound_mailbox_allow_clients_help' => 'Allows clients to send expense documents',
    'inbound_mailbox_whitelist' => 'Inbound sender allow list',
    'inbound_mailbox_whitelist_help' => 'Comma separated list of emails that should be allowed to send emails for processing',
    'inbound_mailbox_blacklist' => 'Inbound sender banned list',
    'inbound_mailbox_blacklist_help' => 'Comma separate list of emails that are disallowed to send emails for processing',
    'inbound_mailbox_allow_unknown' => 'Allow All Senders',
    'inbound_mailbox_allow_unknown_help' => 'Allow anyone to send an expense email for processing',
    'quick_actions' => 'Quick Actions',
    'end_all_sessions_help' => 'Logs out all users and requires all active users to reauthenticate.',
    'updated_records' => 'Updated Records',
    'vat_not_registered' => 'Seller not VAT registered',
    'small_company_info' => 'No disclosure of sales tax in accordance with § 19 UStG',
    'peppol_onboarding' => 'Looks like it\'s your first time using PEPPOL.',
    'get_started' => 'Get Started',
    'configure_peppol' => 'Configure PEPPOL',
    'step' => 'Step',
    'peppol_whitelabel_warning' => 'White-label license required in order to use einvoicing over the PEPPOL network.',
    'peppol_plan_warning' => 'Enterprise plan required in order to use einvoicing over the PEPPOL network.',
    'peppol_credits_info' => 'Ecredits are required to send and receive einvoices. These are charged on a per document basis.',
    'buy_credits' => 'Buy E Credits',
    'peppol_successfully_configured' => 'PEPPOL successsfully configured.',
    'peppol_not_paid_message' => 'Enterprise plan required for PEPPOL. Please upgrade your plan.',
    'peppol_country_not_supported' => 'PEPPOL network not yet available for this country.',
    'peppol_disconnect' => 'Disconnect from the PEPPOL network',
    'peppol_disconnect_short' => 'Disconnect from PEPPOL.',
    'peppol_disconnect_long' => 'Your VAT number will be withdrawn from the PEPPOL network after disconnecting. You will be unable to send or receive edocuments.',
    'log_duration_words' => 'Time log duration in words',
    'log_duration' => 'Time log duration',
    'merged_vendors' => 'Successfully merged vendors',
    'hidden_taxes_warning' => 'Somes taxes are hidden due to current tax settings. :link',
    'tax3' => 'Third Tax',
    'negative_payment_warning' => 'Are you sure you want to create a negative payment? This cannot be used as a credit or payment.',
    'currency_bermudian_dollar' => 'Bermudian Dollar',
    'currency_central_african_cfa_franc' => 'Central African CFA Franc',
    'currency_congolese_franc' => 'Congolese Franc',
    'currency_djiboutian_franc' => 'Djiboutian Franc',
    'currency_eritrean_nakfa' => 'Eritrean Nakfa',
    'currency_falkland_islands_pound' => 'Falkland Islands Pound',
    'currency_guinean_franc' => 'Guinean Franc',
    'currency_iraqi_dinar' => 'Iraqi Dinar',
    'currency_lesotho_loti' => 'Lesotho Loti',
    'currency_mongolian_tugrik' => 'Mongolian Tugrik',
    'currency_seychellois_rupee' => 'Seychellois Rupee',
    'currency_solomon_islands_dollar' => 'Solomon Islands Dollar',
    'currency_somali_shilling' => 'Somali Shilling',
    'currency_south_sudanese_pound' => 'South Sudanese Pound',
    'currency_sudanese_pound' => 'Sudanese Pound',
    'currency_tajikistani_somoni' => 'Tajikistani Somoni',
    'currency_turkmenistani_manat' => 'Turkmenistani Manat',
    'currency_uzbekistani_som' => 'Uzbekistani Som',
    'payment_status_changed' => 'Please note that the status of your payment has been updated. We recommend refreshing the page to view the most current version.',
    'credit_status_changed' => 'Please note that the status of your credit has been updated. We recommend refreshing the page to view the most current version.',
    'credit_updated' => 'Credit Updated',
    'payment_updated' => 'Payment Updated',
    'search_placeholder' => 'Find invoices, clients, and more',
    'invalid_vat_number' => "The VAT number is not valid for the selected country. Format should be Country Code followed by number only ie, DE123456789",
    'acts_as_sender' => 'Send E-Invoices',
    'acts_as_receiver' => 'Receive E-Invoices',
    'peppol_token_generated' => 'PEPPOL token successfully generated.',
    'peppol_token_description' => 'Token is used as another step to make sure invoices are sent securely. Unlike white-label licenses, token can be rotated at any point without need to wait on Invoice Ninja support.',
    'peppol_token_warning' => 'You need to generate a token to continue.',
    'generate_token' => 'Generate Token',
    'total_credits_amount' => 'Amount of Credits',
    'sales_above_threshold' => 'Sales above threshold',
    'changing_vat_and_id_number_note' => 'You can\'t change your VAT number or ID number once PEPPOL is set up.',
    'iban_help' => 'The full IBAN number',
    'bic_swift' => 'BIC/Swift code',
    'bic_swift_help' => 'The Bank identifer',
    'payer_bank_account' => 'Payer Bank Account Number',
    'payer_bank_account_help' => 'The bank account number of the payer',
    'bsb_sort' => 'BSB / Sort Code',
    'bsb_sort_help' => 'Bank Branch Code',
    'card_type' => 'Card Type',
    'card_type_help' => 'ie. VISA, AMEX',
    'card_number_help' => 'last 4 digits only',
    'card_holder' => 'Card Holder Name',
    'tokenize' => 'Tokenize',
    'tokenize_help' => 'Tokenize payment method for future use.',
    'credit_card_stripe_help' => 'Accept credit card payments using Stripe.',
    'bank_transfer_stripe_help' => 'ACH direct debit. USD payments, instant verification available.',
    'alipay_stripe_help' => 'Alipay allows users in China to pay securely using their mobile wallets.',
    'sofort_stripe_help' => 'Sofort is a popular European payment method that enables bank transfers in real-time, primarily used in Germany and Austria.',
    'apple_pay_stripe_help' => 'Apple/Google Pay for users with Apple/Android devices, using saved card information for easy checkout.',
    'sepa_stripe_help' => 'SEPA Direct Debit (Single Euro Payments Area).',
    'bancontact_stripe_help' => 'Bancontact is a widely used payment method in Belgium.',
    'ideal_stripe_help' => 'iDEAL is the most popular payment method in the Netherlands.',
    'giropay_stripe_help' => 'Giropay is a German payment method that facilitates secure and immediate online bank transfers.',
    'przelewy24_stripe_help' => 'Przelewy24 is a common payment method in Poland.',
    'direct_debit_stripe_help' => 'Stripe Bank Transfers using Stripes virtual bank accounts, available in Japan, UK, USA, Europe and Mexico. Ensure this is enabled in Stripe!',
    'eps_stripe_help' => 'EPS is an Austrian online payment system.',
    'acss_stripe_help' => 'ACSS (Automated Clearing Settlement System) Direct Debit for Canadian bank accounts.',
    'becs_stripe_help' => 'BECS Direct Debit for Australian bank accounts.',
    'klarna_stripe_help' => 'Klarna buy now and pay later in installments or on a set schedule.',
    'bacs_stripe_help' => 'BACS Direct Debit for UK bank accounts, commonly used for subscription billing.',
    'fpx_stripe_help' => 'FPX is a popular online payment method in Malaysia.',
    'payment_means' => 'Payment Means',
    'act_as_sender' => 'Send E-Invoice',
    'act_as_receiver' => 'Receive E-Invoice',
    'saved_einvoice_details' => 'Saved E-Invoice Settings',
    'add_license_to_env' => 'We\'ll need your license key for future communication to our services. Make sure to LICENSE_KEY as environment variable.',
    'white_label_license_not_present' => 'License not found. Make sure to set LICENSE_KEY as environment variable.',
    'white_label_license_not_found' => 'White label license not found.',
    'details_update_info' => 'We\'ll update your company details with the provided information.',
    'sales_above_threshold' => 'Sales above threshold',
    'client_address_required' => 'Full client address is required for E-invoicing',
    'connected' => 'Connected',
    'email_count_quotes' => 'Email :count quotes',
    'activity_146' => 'E-Invoice :invoice for :client successfully delivered! :notes',
    'activity_147' => 'E-Invoice :invoice for :client failed delivery. :notes',
    'peppol_routing_problem' => 'Routing problem. No recipient/destination found.',
    'peppol_sending_failed' => 'Technical delivery problem. Retry not possible',
    'peppol_cleared_for_sending' => 'Cleared by tax authority, sending to receiver',
    'account_holder' => 'Account Name',
    'account_holder_help' => 'The name of the account',
    'activity_148' => 'E-Expense :expense received from :vendor',
    'additional_tax_identifiers' => 'Additional Tax Identifiers',
    'additional_tax_identifiers_help' => 'If you are registered for VAT in other regions, you can add your VAT numbers for those regions here.',
    'configure' => 'Configure',
    'new_identifier' => 'New VAT Number',
    'notification_credits_low' => 'Warning! Your credit balance is low.',
    'notification_credits_low_text' => 'Please add credits to your account to avoid interruption of services.',
    'notification_no_credits' => 'Warning! Your credit balance is empty.',
    'notification_no_credits_text' => 'Please add credits to your account to avoid interruption of services.',
    'saved_comment' => 'Comment Saved',
    'acts_as_must_be_true' => 'Either "Send E-Invoice" or "Receive E-Invoice" (or both) must be selected.',
    'delete_identifier' => 'Delete identifier',
    'delete_identifier_description' => 'Deleting this identifier will remove it from the system. Make sure this is the desired action before proceeding.',
    'einvoice_something_went_wrong' => 'Oops! Something went wrong. Contact <NAME_EMAIL> for more information.',
    'download_ready' => 'Your Download is now ready! [ :message ]',
    'notification_quote_reminder1_sent_subject' => 'Reminder 1 for Quote :invoice was sent to :client',
    'custom_reminder_sent' => 'Custom reminder was sent to :client',
    'use_system_fonts' => 'Use System Fonts',
    'use_system_fonts_help' => 'Override the standard fonts with those from the web browser',
    'active_tasks' => 'Active Tasks',
    'enable_notifications' => 'Enable Notifications',
    'enable_public_notifications' => 'Enable Public Notifications',
    'enable_public_notifications_help' => 'Enable real-time notifications from Invoice Ninja.',
    'navigate' => 'Navigate',
    'calculate_taxes_warning' => 'This action will enable line item taxes and disable total taxes. Any open invoices may be recalculated with the new settings!',
    'activity_149' => ':user emailed credit :credit for :client to :contact',
    'email_history_empty' => 'No email history found, this feature only available when sending with Postmark/Mailgun.',
    'e_invoicing' => 'E-Invoicing',
    'einvoice_token_not_found' => 'E-invoicing token not found. Please go to Settings > E-invoice and regenerate token.',
    'regenerate' => 'Regenerate',
    'subscription_unavailable' => 'This item is no longer available',
    'currency_samoan_tala' => 'Samoan Tala',
    'confirm_duplicate_gateway' => 'Are you sure you want to create another connection?',
    'clients_limit' => 'You have reached your client limit. Please upgrade your plan.',
    'remaining_hours' => 'Remaining Hours',
    'just_now' => 'Just Now',
    'yesterday' => 'Yesterday',
    'enable_client_profile_update' => 'Allow clients to update their profile',
    'enable_client_profile_update_help' => 'Allow clients to update their profile information from the client portal',
    'preference_product_notes_for_html_view' => 'Use Item Notes for HTML View',
    'preference_product_notes_for_html_view_help' => 'Preference the item Description over the item title if displaying the invoice in HTML.',
    'project_report' => 'Project Report',
    'unlock_invoice_documents_after_payment' => 'Unlock Documents After Payment',
    'unlock_invoice_documents_after_payment_help' => 'Allows client access to invoice documents when an invoice has been paid',
    'quickbooks' => 'Quickbooks',
    'disable_emails' => 'Disable Emails',
    'disable_emails_error' => 'You are not authorized to send emails',
    'disable_emails_help' => 'Prevents a user from sending emails from the system',
    'add_location' => 'Add Location',
    'updated_location' => 'Updated Location',
    'created_location' => 'Created Location',
);

return $lang;
