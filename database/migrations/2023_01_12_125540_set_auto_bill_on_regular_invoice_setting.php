<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if columns exist before adding
        if (!Schema::hasColumn('accounts', 'is_trial')) {
            Schema::table('accounts', function (Blueprint $table) {
                $table->boolean('is_trial')->default(false);
            });
        }

        if (!Schema::hasColumn('companies', 'invoice_task_hours')) {
            Schema::table('companies', function (Blueprint $table) {
                $table->boolean('invoice_task_hours')->default(false);
            });
        }

        // Skip column drops for SQLite due to index issues
        if (config('database.default') !== 'sqlite') {
            Schema::table('schedulers', function (Blueprint $table) {
                $table->dropColumn('repeat_every');
                $table->dropColumn('start_from');
                $table->dropColumn('scheduled_run');
                $table->dropColumn('action_name');
                $table->dropColumn('action_class');
                $table->dropColumn('paused');
                $table->dropColumn('company_id');
            });
        }


        // Check if columns exist before adding to schedulers table
        Schema::table('schedulers', function (Blueprint $table) {
            if (!Schema::hasColumn('schedulers', 'company_id')) {
                $table->unsignedInteger('company_id');
            }
            if (!Schema::hasColumn('schedulers', 'is_paused')) {
                $table->boolean('is_paused')->default(false);
            }
            if (!Schema::hasColumn('schedulers', 'frequency_id')) {
                $table->unsignedInteger('frequency_id')->nullable();
            }
            if (!Schema::hasColumn('schedulers', 'next_run')) {
                $table->datetime('next_run')->nullable();
            }
            if (!Schema::hasColumn('schedulers', 'next_run_client')) {
                $table->datetime('next_run_client')->nullable();
            }
            if (!Schema::hasColumn('schedulers', 'user_id')) {
                $table->unsignedInteger('user_id');
            }
            if (!Schema::hasColumn('schedulers', 'name')) {
                $table->string('name', 191);
            }
            if (!Schema::hasColumn('schedulers', 'template')) {
                $table->string('template', 191);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
