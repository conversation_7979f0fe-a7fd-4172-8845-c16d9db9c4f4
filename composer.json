{"name": "ozoo/ozoo-erp", "description": "Complete ERP solution with invoicing, expenses & time-tracking built with Laravel", "keywords": ["erp", "laravel", "invoicing", "time tracking", "expenses", "CRM", "Credit card billing", "projects", "tasks", "business management", "quotes", "purchase orders", "billing", "invoices", "subscriptions"], "license": "Proprietary", "authors": [{"name": "Ozoo ERP Team", "email": "<EMAIL>"}], "type": "project", "require": {"php": ">=8.2", "ext-curl": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-simplexml": "*", "afosto/yaac": "^1.5", "asm/php-ansible": "dev-main", "authorizenet/authorizenet": "^2.0", "awobaz/compoships": "^2.1", "aws/aws-sdk-php": "^3.319", "babenkoivan/elastic-scout-driver": "^4.0", "bacon/bacon-qr-code": "^2.0", "beganovich/snappdf": "dev-master", "braintree/braintree_php": "^6.23", "btcpayserver/btcpayserver-greenfield-php": "^2.6", "checkout/checkout-sdk-php": "^3.0", "doctrine/dbal": "^4.0", "eway/eway-rapid-php": "^1.3", "fakerphp/faker": "^1.14", "getbrevo/brevo-php": "^1.0", "gocardless/gocardless-pro": "^4.12", "google/apiclient": "^2.18", "guzzlehttp/guzzle": "^7.2", "halaxa/json-machine": "^0.7.0", "hashids/hashids": "^4.0", "hedii/laravel-gelf-logger": "^9", "horstoeko/orderx": "dev-master", "horstoeko/zugferd": "^1", "horstoeko/zugferdvisualizer": "^1", "hyvor/php-json-exporter": "^0.0.3", "imdhemy/laravel-purchases": "^1.7", "intervention/image": "^2.5", "invoiceninja/einvoice": "dev-main", "invoiceninja/inspector": "^3.0", "invoiceninja/ubl_invoice": "^2", "josemmo/facturae-php": "^1.7", "laracasts/presenter": "^0.2.1", "laravel/framework": "^v11", "laravel/octane": "^2.6", "laravel/scout": "^10.11", "laravel/slack-notification-channel": "^3", "laravel/socialite": "^5", "laravel/tinker": "^2.7", "laravel/ui": "^4.0", "league/csv": "^9.6", "league/flysystem-aws-s3-v3": "^3.0", "league/fractal": "^0.20.0", "livewire/livewire": "^3", "mailgun/mailgun-php": "^3.6", "microsoft/microsoft-graph": "^1.69", "mindee/mindee": "^1.8", "mollie/mollie-api-php": "^2.36", "nelexa/zip": "^4.0", "nordigen/nordigen-php": "^1.1", "nwidart/laravel-modules": "^11.0", "phpoffice/phpspreadsheet": "^2.2", "pragmarx/google2fa": "^8.0", "predis/predis": "^2", "psr/http-message": "^1.0", "pusher/pusher-php-server": "^7.2", "quickbooks/v3-php-sdk": "^6.2", "razorpay/razorpay": "2.*", "sentry/sentry-laravel": "^4", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.3", "socialiteproviders/apple": "dev-master", "socialiteproviders/microsoft": "^4.1", "sprain/swiss-qr-bill": "^4.3", "square/square": "30.0.0.*", "stripe/stripe-php": "^12", "symfony/brevo-mailer": "^7.1", "symfony/http-client": "^7.0.3", "symfony/mailer": "7.1.6", "symfony/mailgun-mailer": "^7.1", "symfony/postmark-mailer": "^7.1", "turbo124/beacon": "^2", "twig/extra-bundle": "^3.18", "twig/intl-extra": "^3.7", "twig/markdown-extra": "^3.18", "twig/twig": "^3.14", "twilio/sdk": "^6.40", "wikimedia/composer-merge-plugin": "^2.1", "wildbit/postmark-php": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "barryvdh/laravel-ide-helper": "^3.0", "brianium/paratest": "^7", "filp/whoops": "^2.7", "friendsofphp/php-cs-fixer": "^3.14", "laracasts/cypress": "^3.0", "larastan/larastan": "^2", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^11", "spatie/laravel-ignition": "^2.0", "spaze/phpstan-stripe": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/TranslationHelper.php", "app/Helpers/Generic.php", "app/Helpers/ClientPortal.php"], "classmap": []}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["Modules/*/composer.json"], "recurse": true, "replace": false, "ignore-duplicates": false, "merge-dev": true, "merge-extra": false, "merge-extra-deep": false, "merge-replace": true, "merge-scripts": false}}, "scripts": {"post-install-cmd": [], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\"", "@php artisan key:generate", "@php artisan storage:link"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "format": ["vendor/bin/php-cs-fixer fix"], "update-snapshots": "./phpunit --no-coverage -d --update-snapshots", "lint": "pint", "test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "pcf": "vendor/bin/php-cs-fixer fix --verbose"}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "repositories": [{"type": "vcs", "url": "https://github.com/turbo124/apple"}, {"type": "vcs", "url": "https://github.com/invoiceninja/einvoice"}, {"type": "vcs", "url": "https://github.com/turbo124/orderx"}, {"type": "vcs", "url": "https://github.com/beganovich/php-ansible"}, {"type": "vcs", "url": "https://github.com/turbo124/snappdf"}], "minimum-stability": "dev", "prefer-stable": true}