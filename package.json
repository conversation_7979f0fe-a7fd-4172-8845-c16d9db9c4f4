{"name": "@ozoo/ozoo-erp", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "production": "vite build"}, "devDependencies": {"@babel/compat-data": "7.15.0", "@babel/plugin-proposal-class-properties": "^7.14.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.18", "cypress": "^12.5.1", "laravel-mix-purgecss": "^6.0.0", "laravel-vite-plugin": "^0.8.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^4.4.9", "vite-plugin-static-copy": "^0.17.0", "vue-template-compiler": "^2.6.14"}, "dependencies": {"axios": "^0.25", "card-js": "^1.0.13", "card-validator": "^8.1.1", "chartjs": "^0.3.24", "chartjs-plugin-datalabels": "^2.2.0", "clipboard": "^2.0.10", "cross-env": "^7.0.3", "jsignature": "^2.1.3", "json-formatter-js": "^2.3.4", "laravel-mix": "^6.0.34", "linkify-urls": "^4.0.0", "lodash": "^4.17.21", "resolve-url-loader": "^4.0.0", "sass": "^1.43.4", "sass-loader": "^12.3.0", "signature_pad": "^5.0.2"}, "type": "module"}