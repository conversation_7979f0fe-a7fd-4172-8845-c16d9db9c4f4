<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="30" cy="30" r="25" fill="url(#logoGradient)"/>
  
  <!-- Letter O -->
  <circle cx="30" cy="30" r="12" fill="none" stroke="white" stroke-width="3"/>
  
  <!-- Text -->
  <text x="70" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1E40AF">Ozoo</text>
  <text x="70" y="45" font-family="Arial, sans-serif" font-size="14" fill="#6B7280">ERP</text>
</svg>
