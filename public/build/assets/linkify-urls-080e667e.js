import{g as m}from"./_commonjsHelpers-725317a4.js";const l=e=>e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;");var c=(e,...t)=>{if(typeof e=="string")return l(e);let r=e[0];for(const[n,a]of t.entries())r=r+l(String(a))+e[n+1];return r};function p(e){const t=[];for(let[r,n]of Object.entries(e)){if(n===!1)continue;Array.isArray(n)&&(n=n.join(" "));let a=c(r);n!==!0&&(a+=`="${c(String(n))}"`),t.push(a)}return t.length>0?" "+t.join(" "):""}const g=["area","base","br","col","embed","hr","img","input","link","menuitem","meta","param","source","track","wbr"];var d=g;const y=m(d),i=e=>e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;");function h(e,...t){if(typeof e=="string")return i(e);let r=e[0];for(const[n,a]of t.entries())r=r+i(String(a))+e[n+1];return r}const b=new Set(y);function v({name:e="div",attributes:t={},html:r="",text:n}={}){if(r&&n)throw new Error("The `html` and `text` options are mutually exclusive");const a=n?h(n):r;let u=`<${e}${p(t)}>`;return b.has(e)||(u+=`${a}</${e}>`),u}const f=()=>new RegExp("((?<!\\+)https?:\\/\\/(?:www\\.)?(?:[-\\w.]+?[.@][a-zA-Z\\d]{2,}|localhost)(?:[-\\w.:%+~#*$!?&/=@]*?(?:,(?!\\s))*?)*)","g"),s=(e,t)=>v({name:"a",attributes:{href:"",...t.attributes,href:e},text:typeof t.value>"u"?e:void 0,html:typeof t.value>"u"?void 0:typeof t.value=="function"?t.value(e):t.value}),w=e=>document.createRange().createContextualFragment(e),x=(e,t)=>e.replace(f(),r=>s(r,t)),E=(e,t)=>{const r=document.createDocumentFragment();for(const[n,a]of Object.entries(e.split(f())))n%2?r.append(w(s(a,t))):a.length>0&&r.append(a);return r};function o(e,t){if(t={attributes:{},type:"string",...t},t.type==="string")return x(e,t);if(t.type==="dom")return E(e,t);throw new TypeError("The type option must be either `dom` or `string`")}/**
 * Ozoo ERP - Business Management System
 *
 * @copyright Copyright (c) 2025. Ozoo ERP
 *
 * @license Proprietary
 */document.querySelectorAll("[data-ref=entity-terms]").forEach(e=>{o==="function"&&(e.innerHTML=o(e.innerText,{attributes:{target:"_blank",class:"text-primary"}}))});
