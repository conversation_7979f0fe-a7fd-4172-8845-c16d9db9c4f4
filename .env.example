APP_NAME="Ozoo ERP"
APP_ENV=production
APP_KEY=base64:RR++yx2rJ9kdxbdh3+AmbHLDQu+Q76i++co9Y8ybbno=
APP_DEBUG=false

APP_URL=http://localhost
REACT_URL=http://localhost:3001

DB_CONNECTION=mysql
MULTI_DB_ENABLED=false

DB_HOST=localhost
DB_DATABASE=ninja
DB_USERNAME=ninja
DB_PASSWORD=ninja
DB_PORT=3306

DEMO_MODE=false

BROADCAST_DRIVER=log
LOG_CHANNEL=stack
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=smtp
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

POSTMARK_API_TOKEN=
REQUIRE_HTTPS=false

GOOGLE_MAPS_API_KEY=
ERROR_EMAIL=
TRUSTED_PROXIES=
SCOUT_DRIVER=null 

OZOO_ENVIRONMENT=selfhost

#options - snappdf / phantom / hosted_ozoo
PDF_GENERATOR=snappdf

PHANTOMJS_KEY='a-demo-key-with-low-quota-per-ip-address'
PHANTOMJS_SECRET=secret

UPDATE_SECRET=secret

DELETE_PDF_DAYS=60
DELETE_BACKUP_DAYS=60

COMPOSER_AUTH='{"github-oauth": {"github.com": "${{ secrets.GITHUB_TOKEN }}"}}'

GOOGLE_PLAY_PACKAGE_NAME=
APPSTORE_PASSWORD=

MICROSOFT_CLIENT_ID=
MICROSOFT_CLIENT_SECRET=
MICROSOFT_REDIRECT_URI=

APPLE_CLIENT_ID=
APPLE_CLIENT_SECRET=
APPLE_REDIRECT_URI=

NORDIGEN_SECRET_ID=
NORDIGEN_SECRET_KEY=

GOCARDLESS_CLIENT_ID=
GOCARDLESS_CLIENT_SECRET=

OPENEXCHANGE_APP_ID=
