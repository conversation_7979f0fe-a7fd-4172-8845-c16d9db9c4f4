<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// Bypass setup route
Route::get('/setup-bypass', function () {
    // Mark setup as complete
    file_put_contents(storage_path('installed'), 'Ozoo ERP Setup Complete');
    
    // Redirect to login
    return redirect('/')->with('message', 'Ozoo ERP setup completed! Please login with: <EMAIL> / admin123');
});

// Override setup check
Route::get('/setup-check', function () {
    return response()->json(['setup_complete' => true]);
});
