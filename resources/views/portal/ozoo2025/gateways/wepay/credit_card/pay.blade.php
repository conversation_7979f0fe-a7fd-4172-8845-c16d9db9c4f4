@extends('portal.ninja2020.layout.payments', ['gateway_title' => ctrans('texts.credit_card'), 'card_title' => ctrans('texts.credit_card')])

@section('gateway_head')
    <meta name="wepay-environment" content="{{ config('ninja.wepay.environment') }}">
    <meta name="wepay-action" content="payment">
    <meta name="wepay-client-id" content="{{ config('ninja.wepay.client_id') }}">

    <meta name="contact-email" content="{{ $contact->email }}">
    <meta name="client-postal-code" content="{{ $contact->client->postal_code }}">
    <meta name="country_code" content="{{$country_code}}">
    <script src="https://code.jquery.com/jquery-1.11.3.min.js"></script>

    <script src="{{ asset('build/public/js/card-js.min.js/card-js.min.js') }}"></script>
    <link href="{{ asset('build/public/css/card-js.min.css/card-js.min.css') }}" rel="stylesheet" type="text/css">

    <script type="text/javascript" src="https://static.wepay.com/min/js/tokenization.4.latest.js"></script>
@endsection

@section('gateway_content')
    <form action="{{ route('client.payments.response') }}" method="post" id="server-response">
        @csrf
        <input type="hidden" name="gateway_response">
        <input type="hidden" name="store_card" id="store_card">
        <input type="hidden" name="payment_hash" value="{{ $payment_hash }}">

        <input type="hidden" name="company_gateway_id" value="{{ $gateway->getCompanyGatewayId() }}">
        <input type="hidden" name="payment_method_id" value="1">

        <input type="hidden" name="token" id="token" value="">
        <input type="hidden" name="credit_card_id" id="credit_card_id" value="">
    </form>

    <div class="alert alert-failure mb-4" hidden id="errors"></div>

    @component('portal.ninja2020.components.general.card-element', ['title' => ctrans('texts.payment_type')])
        {{ ctrans('texts.credit_card') }}
    @endcomponent

    @include('portal.ninja2020.gateways.includes.payment_details')

    @component('portal.ninja2020.components.general.card-element', ['title' => ctrans('texts.pay_with')])
        @if(count($tokens) > 0)
            @foreach($tokens as $token)
                <label class="mr-4">
                    <input
                        type="radio"
                        data-token="{{ $token->token }}"
                        name="payment-type"
                        class="form-radio cursor-pointer toggle-payment-with-token"/>
                    <span class="ml-1 cursor-pointer">**** {{ $token->meta?->last4 }}</span>
                </label>
            @endforeach
        @endisset

        <label>
            <input
                type="radio"
                id="toggle-payment-with-credit-card"
                class="form-radio cursor-pointer"
                name="payment-type"
                checked/>
            <span class="ml-1 cursor-pointer">{{ __('texts.new_card') }}</span>
        </label>
    @endcomponent

    @include('portal.ninja2020.gateways.includes.save_card')

    @include('portal.ninja2020.gateways.wepay.includes.credit_card')

    @include('portal.ninja2020.gateways.includes.pay_now')
@endsection

@section('gateway_footer')
    <script>

        document.addEventListener('livewire:init', () => {

            Livewire.on('passed-required-fields-check', (event) => {
                if (event.hasOwnProperty('client_postal_code')) {
                    document.querySelector('meta[name=client-postal-code]').content = event.client_postal_code;
                }
            });

        });

    </script>

    @vite('resources/js/clients/payments/wepay-credit-card.js')
@endsection

@push('footer')
<script defer>
 
$(function() {

    document.getElementsByClassName("expiry")[0].addEventListener('change', function() {

    str = document.getElementsByClassName("expiry")[0].value.replace(/\s/g, '');
    const expiryArray = str.split("/");

    document.getElementsByName('expiry-month')[0].value = expiryArray[0];
    document.getElementsByName('expiry-year')[0].value = expiryArray[1];

    });

});

</script>
@endpush